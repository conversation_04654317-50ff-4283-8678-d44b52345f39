﻿using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web.Export
{
    public class ExportLeadHelper
    {
        public static List<ExportLeadFormattedDto> ConvertToLeadDto(
            List<Domain.Entities.Lead> leads,
            List<Identity.Users.UserDetailsDto> users,
            List<CustomMasterLeadStatus> masterLeadStatuses = null,
            List<MasterPropertyType> masterPropertyTypes = null,
            List<NotesDetails> notes = null, bool? IsWithNotes = null,
            bool? shouldRenameSiteVisitColumn = null, List<CommunicationDetails> communication = null,
            bool? isWithFacebookProperties = null
            )
        {
            List<ExportLeadFormattedDto> exportLeadDto = new List<ExportLeadFormattedDto>();
            if (leads.Any())
            {
                foreach (var lead in leads)
                {
                    var leadDto = lead.Adapt<ExportLeadFormattedDto>();
                    leadDto.ShouldRenameSiteVisitColumn = shouldRenameSiteVisitColumn;
                    var enquiry = lead.Enquiries.FirstOrDefault();


                    if (lead.Appointments?.Any() ?? false)
                    {
                        leadDto.SiteVisitsDoneCount = lead.Appointments.Where(i => i.Type == AppointmentType.SiteVisit && i.IsDone).Count();
                        leadDto.SiteVisitsNotDoneCount = lead.Appointments.Where(i => i.Type == AppointmentType.SiteVisit && !i.IsDone).Count();
                        leadDto.MeetingsDoneCount = lead.Appointments.Where(i => i.Type == AppointmentType.Meeting && i.IsDone).Count();
                        leadDto.MeetingsNotDoneCount = lead.Appointments.Where(i => i.Type == AppointmentType.Meeting && !i.IsDone).Count();
                    }
                    if (enquiry != null)
                    {
                        enquiry.Adapt(leadDto);
                        // leadDto.PropertyType = masterPropertyTypes?.FirstOrDefault(i => i.Id == enquiry.PropertyType?.BaseId)?.DisplayName ?? string.Empty;
                        leadDto.PropertyType = masterPropertyTypes?.FirstOrDefault(i => i.Id == enquiry?.PropertyTypes?.Select(i => i.BaseId).FirstOrDefault())?.DisplayName ?? string.Empty;

                        //leadDto.PropertySubType = enquiry.PropertyType?.Level == 1 ? enquiry.PropertyType?.DisplayName : string.Empty;

                        var propertySubType = enquiry.PropertyTypes?.Where(i => i.Level == 1).Select(i => i.DisplayName).ToList();
                        //if (enquiry.Address != null)
                        //{
                        //    enquiry.Address.Adapt(leadDto);
                        //}
                        if (enquiry?.Addresses?.Any() ?? false)
                        {
                            enquiry.Addresses.Adapt(leadDto);
                        }
                        leadDto.EnquiryTypes = string.Join(",", enquiry?.EnquiryTypes?.Select(i => i.ToString())?.ToList() ?? new List<string?>());
                        leadDto.BHKs = string.Join(",", enquiry?.BHKs?.Select(i => i.ToString())?.ToList() ?? new List<string?>());
                        leadDto.BHKTypes = string.Join(",", enquiry?.BHKTypes?.Select(i => i.ToString())?.ToList() ?? new List<string?>());
                        leadDto.SubLocality = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.SubLocality))?.Select(i => i.SubLocality)?.Distinct().ToList() ?? new List<string>());
                        leadDto.Locality = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.Locality))?.Select(i => i.Locality)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.District = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.District))?.Select(i => i.District)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.City = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.City))?.Select(i => i.City)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.State = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.State))?.Select(i => i.State)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.Country = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.Country))?.Select(i => i.Country)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.PostalCode = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.PostalCode))?.Select(i => i.PostalCode)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.PropertySubType = propertySubType != null && propertySubType.Any() ? string.Join(", ", propertySubType) : string.Empty;
                        leadDto.Community = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.Community))?.Select(i => i.Community)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.SubCommunity = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.SubCommunity))?.Select(i => i.SubCommunity)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.TowerName = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.TowerName))?.Select(i => i.TowerName)?.Distinct().ToList() ?? new List<string?>());
                        leadDto.Beds = string.Join(",", enquiry?.Beds?.Select(i => i.ToString())?.ToList() ?? new List<string?>());

                    }
                    leadDto.Adapt(lead.TagInfo);
                    var user = users.FirstOrDefault(i => i.Id == lead.AssignTo);
                    if (user != null)
                    {
                        leadDto.AssignedUserName = user.FirstName + " " + user.LastName;
                        leadDto.AssignedUserEmail = user.Email;
                        leadDto.AssignedUserPhoneNumber = user.PhoneNumber;

                    }
                    var secondayUser = users.FirstOrDefault(i => i.Id == lead.SecondaryUserId);
                    if (secondayUser != null)
                    {
                        leadDto.SecondaryUserName = secondayUser.FirstName + " " + secondayUser.LastName;
                    }
                    var originalUser = users.FirstOrDefault(i => i.Id == lead.OriginalOwner);
                    if (originalUser != null)
                    {
                        leadDto.OriginalUser = originalUser.FirstName + " " + originalUser.LastName;
                    }

                    if (IsWithNotes == true)
                    {
                        var notesForLead = notes
                            .Where(notes => notes.LeadId == lead.Id)
                            .ToList();

                        if (notesForLead?.Any() ?? false)
                        {
                            leadDto.NotesDetails = new List<NotesDetails>(notesForLead);
                        }
                    }
                    try
                    {
                        if (isWithFacebookProperties == true && !string.IsNullOrWhiteSpace(lead.AdditionalProperties?.ToString()))
                        {
                            var jsonString = JsonConvert.SerializeObject(lead.AdditionalProperties);

                            // Deserialize into the strongly typed DTO
                            var fbDto = JsonConvert.DeserializeObject<FacebookDetailsDto>(jsonString);
                            leadDto.FacebookProperties = fbDto != null ? new List<FacebookDetailsDto> { fbDto } : null;
                        }
                    }
                    catch (Exception ex)
                    {
                        leadDto.FacebookProperties = null;
                    }
                    if (communication?.Any() ?? false)
                    {
                        var communicationDetails = communication?.GroupBy(c => c.LeadId).ToDictionary(g => g.Key, g => g.ToDictionary(c => c.ContactType, c => c)) ??new Dictionary<Guid?, Dictionary<ContactType?, CommunicationDetails>>();
                        if (communicationDetails.TryGetValue(lead.Id, out var leadCommunications))
                        {
                            if (leadCommunications.TryGetValue(ContactType.Call, out var callComm))
                            {
                                leadDto.CallCount = callComm.LeadCount;
                                leadDto.LastCallDoneOn = callComm?.LastModifiedOn?.ToString("dd/MM/yyyy");
                                leadDto.LastCallDoneBy = callComm?.LastDoneByUser;
                            }
                            if (leadCommunications.TryGetValue(ContactType.WhatsApp, out var whatsAppComm))
                            {
                                leadDto.WhatsAppCount = whatsAppComm.LeadCount;
                                leadDto.LastWhatsAppDoneOn = whatsAppComm?.LastModifiedOn?.ToString("dd/MM/yyyy");
                                leadDto.LastWhatsAppDoneBy = whatsAppComm?.LastDoneByUser;
                            }
                            if (leadCommunications.TryGetValue(ContactType.Email, out var emailComm))
                            {
                                leadDto.EmailCount = emailComm.LeadCount;
                                leadDto.LastEmailDoneOn = emailComm?.LastModifiedOn?.ToString("dd/MM/yyyy");
                                leadDto.LastEmailDoneBy = emailComm?.LastDoneByUser;
                            }
                        }
                    }
                    if (lead?.CustomLeadStatus?.BaseId != null && lead?.CustomLeadStatus?.BaseId != Guid.Empty)
                    {
                        leadDto.Status = masterLeadStatuses?.FirstOrDefault(i => i.Id == lead?.CustomLeadStatus.BaseId)?.DisplayName ?? string.Empty;
                        if (shouldRenameSiteVisitColumn == true)
                        {
                            leadDto.Status = "Referral Scheduled";
                        }
                    }
                    else
                    {
                        leadDto.Status = lead?.CustomLeadStatus?.Level == 0 ? lead?.CustomLeadStatus?.DisplayName : string.Empty;
                        if (shouldRenameSiteVisitColumn == true)
                        {
                            leadDto.Status = "Referral Scheduled";
                        }
                    }
                    leadDto.Reason = lead?.CustomLeadStatus?.Level == 1 ? lead?.CustomLeadStatus?.DisplayName : string.Empty;
                    leadDto.ReceivedOn = lead.CreatedOn;
                    leadDto.Projects = string.Join(",", lead?.Projects?.Select(i => i.Name)?.ToList() ?? new List<string?>());
                    leadDto.AgencyName = string.Join(",", lead?.Agencies?.Select(i => i.Name)?.ToList() ?? new List<string?>());
                    leadDto.SerialNumbers = lead?.SerialNumber;
                    leadDto.Properties = string.Join(",", lead?.Properties?.Select(i => i.Title)?.ToList() ?? new List<string?>());
                    var customeraddress = lead?.Address;
                    leadDto.CustomerTowerName = customeraddress?.TowerName ?? string.Empty;
                    leadDto.CustomerSubCommunity = customeraddress?.SubCommunity ?? string.Empty;
                    leadDto.CustomerCommunity = customeraddress?.Community ?? string.Empty;
                    leadDto.CustomerPostalCode = customeraddress?.PostalCode ?? string.Empty;
                    leadDto.CustomerCountry = customeraddress?.Country ?? string.Empty;
                    leadDto.CustomerCity = customeraddress?.City ?? string.Empty;
                    leadDto.CustomerLocality = customeraddress?.SubLocality ?? string.Empty;
                    leadDto.CustomerState = customeraddress?.State ?? string.Empty;
                    leadDto.CustomerSubCommunity = customeraddress?.SubCommunity ?? string.Empty;
                    leadDto.CampaignNames = string.Join(",", lead?.Campaigns?.Select(i => i.Name)?.ToList() ?? new List<string?>());
                    leadDto.ChannelPartnerMobile = string.Join(",", lead?.ChannelPartners?.Select(i => i.ContactNo)?.ToList() ?? new List<string?>());
                    leadDto.ChannelPartnerName = string.Join(",", lead?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new List<string?>());
                    leadDto.ChannelPartnerEmail = string.Join(",", lead?.ChannelPartners?.Select(i => i.Email)?.ToList() ?? new List<string?>());

                    exportLeadDto.Add(leadDto);
                }
            }
            return exportLeadDto;
        }
        public static string ReplaceVariables(string text, Dictionary<string, string> variableSet)
        {
            if (variableSet == null || !variableSet.Any() || string.IsNullOrWhiteSpace(text))
            {
                return text;
            }
            foreach (var variable in variableSet)
            {
                text = text.Replace(variable.Key, String.Format(variable.Value).Replace(" ", "%20") ?? string.Empty);
            }
            return text;
        }
        public static List<NotesDetails> GetAllDatawithNotes(List<NotesDetailsV1> noteDetailsData, int notes)
        {
            var notesData = noteDetailsData
                .SelectMany(item =>
                {
                    var leadId = item.LeadId;
                    var modifiedOn = JsonConvert.DeserializeObject<Dictionary<string, DateTime>>(item.ModifiedOn);
                    var noteses = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.Notes);
                    var lastModifiedByUser = JsonConvert.DeserializeObject<Dictionary<string, string>>(item.LastModifiedByUser);

                    return modifiedOn
                        .Where(kvp => noteses.ContainsKey(kvp.Key) && !string.IsNullOrEmpty(noteses[kvp.Key]))
                        .Select(kvp => new NotesDetails
                        {
                            LeadId = leadId,
                            ModifiedOn = kvp.Value,
                            Notes = noteses[kvp.Key],
                            LastModifiedByUser = lastModifiedByUser.TryGetValue(kvp.Key, out var user) ? user : null
                        });
                })
                .ToList();

            return notesData
                .GroupBy(n => n.LeadId)
                .SelectMany(group => group.OrderByDescending(n => n.ModifiedOn).Take(notes))
                .ToList();
        }

    }
}
