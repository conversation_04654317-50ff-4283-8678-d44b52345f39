﻿using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.Lead.Utils;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Export;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using NodaTime;
using System.Collections.Generic;
using VimeoDotNet.Models;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportLeadsHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var globalSettings = (await _globalSettingsRepository.ListAsync(cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsLeadsExportEnabled)
            {
                ExportLeadTracker? exportTracker = await _exportLeadRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                RunAWSBatchForExportLeadsRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportLeadsRequest>(exportTracker?.Request ?? string.Empty);
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;
                Guid CreatredById = exportTracker.CreatedBy;
                string trackerIdString = CreatredById.ToString();
                ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
                var userDetails1 = _userService.GetAsync(trackerIdString, cancellationToken);
                string firstName = userDetails1.Result.FirstName;
                string lastName = userDetails1.Result.LastName;
                string createdBy = $"{firstName} {lastName}";
                tracker.CreatedBy = createdBy;
                var exportTracker1 = new ExportTrackerDto
                {
                    CreatedBy = createdBy,
                };
                try
                {
                    if (exportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch all required MasterData and Other data
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        //var leadStatuses = new List<MasterLeadStatus>(await _leadStatusRepo.ListAsync(cancellationToken));
                        var customLeadStatuses = new List<CustomMasterLeadStatus>(await _customMastereadStatus.ListAsync(cancellationToken));
                        #endregion
                        //GetAllLeadsRequest? request = JsonConvert.DeserializeObject<GetAllLeadsRequest>(exportTracker?.Request ?? string.Empty);
                        GetAllLeadsByNewFiltersRequest? request = JsonConvert.DeserializeObject<GetAllLeadsByNewFiltersRequest>(exportTracker?.Request ?? string.Empty);
                        if (request != null)
                        {
                            var isAdmin = _dapperRepository.IsAdminAsync(input.CurrentUserId, input.TenantId ?? string.Empty).Result;
                            LeadFilterDto? filtersDto = request.Adapt<LeadFilterDto>();
                            LeadFormettedFilterDto formattedFiltersDto = filtersDto.Adapt<LeadFormettedFilterDto>();
                            List<Guid> AssignTo = request.AssignTo;
                            List<Guid> AssignFrom = request.AssignedFromIds;
                            List<Guid> RestoredByIds = request.RestoredByIds;
                            List<Guid> CreatedByIds = request.CreatedByIds;
                            List<Guid> LastModifiedByIds = request.LastModifiedByIds;
                            List<Guid> LastDeletedBy = request.ArchivedByIds;
                            List<Guid> StatusIds = request.StatusIds;
                            List<Guid> SubStatusIds = request.SubStatusIds;
                            List<Guid> PropertyTypeId = request.PropertyType;
                            List<Guid> PropertySubTypeId = request.PropertySubType;

                            if ((AssignTo?.Count > 0) || (AssignFrom?.Count > 0) || (RestoredByIds?.Count > 0) || (CreatedByIds?.Count > 0) ||
                                (LastModifiedByIds?.Count > 0) || (LastDeletedBy?.Count > 0))
                            {
                                List<string> ConvertGuidToStringAssignTo = AssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringAssignFrom = AssignFrom?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringRestored = RestoredByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringCreated = CreatedByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringLastModified = LastModifiedByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringLastDeleted = LastDeletedBy?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> usersDetailsAssignTo = new List<string>();
                                List<string> usersDetailsAssignFrom = new List<string>();
                                List<string> usersDetailsRestored = new List<string>();
                                List<string> usersDetailsCreated = new List<string>();
                                List<string> usersDetailsLastModified = new List<string>();
                                List<string> usersDetailsLastDeleted = new List<string>();

                                if (ConvertGuidToStringAssignTo.Count > 0)
                                {
                                    var userDetailsAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignTo, cancellationToken);
                                    usersDetailsAssignTo = userDetailsAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringLastDeleted.Count > 0)
                                {
                                    var userDetails = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringLastDeleted, cancellationToken);
                                    usersDetailsLastDeleted = userDetails.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringAssignFrom.Count > 0)
                                {
                                    var userDetailsAssignFrom = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignFrom, cancellationToken);
                                    usersDetailsAssignFrom = userDetailsAssignFrom.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringRestored.Count > 0)
                                {
                                    var userDetailsRestored = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringRestored, cancellationToken);
                                    usersDetailsRestored = userDetailsRestored.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringCreated.Count > 0)
                                {
                                    var userDetailsCreated = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringCreated, cancellationToken);
                                    usersDetailsCreated = userDetailsCreated.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringLastModified.Count > 0)
                                {
                                    var userDetailsLastModified = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringLastModified, cancellationToken);
                                    usersDetailsLastModified = userDetailsLastModified.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }

                                formattedFiltersDto.AssignTo = string.Join(", ", usersDetailsAssignTo);
                                formattedFiltersDto.AssignedFrom = string.Join(", ", usersDetailsAssignFrom);
                                formattedFiltersDto.CreatedBy = string.Join(", ", usersDetailsCreated);
                                formattedFiltersDto.LastModifiedBy = string.Join(", ", usersDetailsLastModified);
                                formattedFiltersDto.RestoredBy = string.Join(", ", usersDetailsRestored);
                                formattedFiltersDto.LastDeletedBy = string.Join(", ", usersDetailsLastDeleted);
                            }

                            if (StatusIds != null && StatusIds.Count > 0)
                            {
                                var displayNames = customLeadStatuses
                                    .Where(i => StatusIds.Contains(i?.MasterLeadStatusId ?? Guid.Empty) && i?.Level == 0)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.Status = string.Join(", ", displayNames);
                            }
                            if (SubStatusIds != null && SubStatusIds.Count > 0)
                            {
                                var displayNames = customLeadStatuses
                                    .Where(i => SubStatusIds.Contains(i?.MasterLeadStatusId ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.SubStatus = string.Join(", ", displayNames);
                            }

                            if (PropertyTypeId != null && PropertyTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => PropertyTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 0)
                                    .Select(status => status?.DisplayName)
                                    .ToList();
                                formattedFiltersDto.PropertyType = string.Join(", ", displayName);
                            }
                            if (PropertySubTypeId != null && PropertySubTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => PropertySubTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.PropertySubType = string.Join(", ", displayName);

                            }

                            List<Guid> subIds = new();
                            request.PageNumber = 1;
                            if (request?.LeadTags?.Any() ?? false)
                            {
                                request.TagFilterDto = ExportLeadsHelper.GetLeadTagFilter(request);
                                request.LeadTags = null;
                            }
                            try
                            {
                                if (request?.AssignTo?.Any() ?? false)
                                {
                                    if (request?.IsWithTeam ?? false)
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, input.TenantId ?? string.Empty)).ToList();
                                    }
                                    else
                                    {
                                        subIds = request?.AssignTo ?? new List<Guid>();
                                    }
                                }
                                else
                                {
                                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(input.CurrentUserId, input.TenantId ?? string.Empty, viewAllLeads:null, isAdmin))?.ToList() ?? new();
                                }

                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "FunctionEntryPoint -> ExportLeadsHandler()",
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                                throw;
                            }
                            var customStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                            var leads = _leadRepository.GetAllLeadsByNewFiltersForWebAsync(request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, input.CurrentUserId, isAdmin: isAdmin, customMasterLeadStatus:customStatus).Result;

                            var ListofLeadIds = leads?.Select(i => i.Id).ToList();
                            List<NotesDetails> res = null;
                            try
                            {
                                var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<NotesDetails>("LeadratBlack", "GetAllNoteswithUpdatedDateV1", new
                                {
                                    leadid = ListofLeadIds,
                                    tenantId = input.TenantId,
                                });
                                res = result.ToList();
                            }
                            catch
                            {


                            }


                            var leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, res.ToList(),shouldRenameSiteVisitColumn:globalSettings.ShouldRenameSiteVisitColumn);
                            var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                            var notesCount = leadDtos.OrderByDescending(i => i.NotesDetails?.Count ?? 0)?.FirstOrDefault()?.NotesDetails?.Count();

                            var fileBytes = ExcelGeneration<ExportLeadFormattedDto>.GenerateExcel<ExportLeadFormattedDto, LeadFormettedFilterDto, ExportTrackerDto>(leadDtos, " Export Leads", formattedFiltersDto, tracker, notesCount ?? 0,false, requestforFileName.TimeZoneId,requestforFileName.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow));
                            string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(requestforFileName.TimeZoneId ?? "Asia/Kolkata");
                            var fileName = ExportLeadsHelper.ConvertToString(request.LeadVisibility) + "-" + (request.FilterType.ToString() == "AllWithNID" ? "All" : request.FilterType.ToString() == "All" ? "Active" : request.FilterType.ToString());
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Leads/{input.TenantId ?? "Default"}", $"Export_Leads_" +input.TenantId + requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes, 0);
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            List<string> toEmails = new();
                            List<string> ccEamils = new();
                            List<string> bccEamils = new();
                            if (exportTracker?.ToRecipients?.Any() ?? false)
                            {
                                toEmails.AddRange(exportTracker.ToRecipients);
                            }
                            if (exportTracker?.CcRecipients?.Any() ?? false)
                            {
                                ccEamils.AddRange(exportTracker.CcRecipients);
                            }
                            if (exportTracker?.BccRecipients?.Any() ?? false)
                            {
                                bccEamils.AddRange(exportTracker.BccRecipients);
                            }
                            var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                            emailSenderDto.To = toEmails;
                            emailSenderDto.Cc = ccEamils;
                            emailSenderDto.Bcc = bccEamils;
                            emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                            emailSenderDto.EmailBody = template;
                            emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                            emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                            await _graphEmailService.SendEmail(emailSenderDto);
                            isSent = true;
                            exportTracker.Count = leads.Count();
                            exportTracker.S3BucketKey = presignedUrl;
                            exportTracker.FileName = $"Export_Leads_"+ requestforFileName.FileName + "(" + timeZoneName + ")" + ".xlsx";
                            exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                            exportTracker.LastModifiedBy = input.CurrentUserId;
                            exportTracker.CreatedBy = input.CurrentUserId;
                            await _exportLeadRepo.UpdateAsync(exportTracker, cancellationToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    exportTracker.Message = ex.Message.ToString();
                    exportTracker.LastModifiedBy = input.CurrentUserId;
                    exportTracker.CreatedBy = input.CurrentUserId;
                    await _exportLeadRepo.UpdateAsync(exportTracker);
                    if (errorEmailTemplate != null && serviceProvider != null && !isSent)
                    {
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (exportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(exportTracker.ToRecipients);
                        }
                        if (exportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(exportTracker.CcRecipients);
                        }
                        if (exportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(exportTracker.BccRecipients);
                        }
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = errorEmailTemplate.Body;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FunctionEntryPoint -> ExportLeadsHandler()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }

        public async Task ExportLeadsByNewFiltersHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var globalSettings = (await _globalSettingsRepository.ListAsync(cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsLeadsExportEnabled)
            {
                ExportLeadTracker? exportTracker = await _exportLeadRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;
                Guid CreatredById = exportTracker.CreatedBy;
                string trackerIdString = CreatredById.ToString();
                ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
                var userDetails1 = _userService.GetAsync(trackerIdString, cancellationToken);
                string firstName = userDetails1.Result.FirstName;
                string lastName = userDetails1.Result.LastName;
                string createdBy = $"{firstName} {lastName}";
                tracker.CreatedBy = createdBy;
                var exportTracker1 = new ExportTrackerDto
                {
                    CreatedBy = createdBy,
                };
                try
                {
                    if (exportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch all required MasterData and Other data
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        var customLeadStatuses = new List<CustomMasterLeadStatus>(await _customMastereadStatus.ListAsync(cancellationToken));
                        #endregion
                        GetAllLeadsByNewFiltersRequest? request = JsonConvert.DeserializeObject<GetAllLeadsByNewFiltersRequest>(exportTracker?.Request ?? string.Empty);
                        RunAWSBatchForExportLeadsByNewFiltersRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportLeadsByNewFiltersRequest>(exportTracker?.Request ?? string.Empty);
                        if (request != null)
                        {
                            var isAdmin = _dapperRepository.IsAdminAsync(input.CurrentUserId, input.TenantId ?? string.Empty).Result;
                            LeadFilterDto? filtersDto = request.Adapt<LeadFilterDto>();
                            LeadFormettedFilterDto formattedFiltersDto = filtersDto.Adapt<LeadFormettedFilterDto>();
                            List<Guid> AssignTo = request?.AssignTo;
                            List<Guid> AssignFrom = request?.AssignedFromIds;
                            List<Guid> RestoredByIds = request?.RestoredByIds;
                            List<Guid> CreatedByIds = request?.CreatedByIds;
                            List<Guid> LastModifiedByIds = request?.LastModifiedByIds;
                            List<Guid> LastDeletedBy = request?.ArchivedByIds;
                            List<Guid> StatusIds = request?.StatusIds;
                            List<Guid> SubStatusIds = request?.SubStatusIds;
                            List<Guid> PropertyTypeId = request?.PropertyType;
                            List<Guid> PropertySubTypeId = request?.PropertySubType;
                            List<Guid> HistoryAssignTo = request?.HistoryAssignedToIds;
                            List<Guid> SecondaryAssignTo = request?.SecondaryUsers;
                            List<Guid> SecondaryAssignFrom = request?.SecondaryFromIds;
                            List<Guid> DoneBy = request?.DoneBy;
                            if ((AssignTo?.Count > 0) || (AssignFrom?.Count > 0) || (RestoredByIds?.Count > 0) || (CreatedByIds?.Count > 0) ||
                             (LastModifiedByIds?.Count > 0) || (LastDeletedBy?.Count > 0) || (SecondaryAssignTo?.Count > 0) || (SecondaryAssignFrom?.Count > 0)
                             || (HistoryAssignTo?.Count > 0) || (DoneBy?.Count > 0))
                            {
                                List<string> ConvertGuidToStringAssignTo = AssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringAssignFrom = AssignFrom?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringRestored = RestoredByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringCreated = CreatedByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringLastModified = LastModifiedByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringLastDeleted = LastDeletedBy?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringHistoryAssignTo = HistoryAssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringSecondaryAssignTo = SecondaryAssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringSecondaryAssignFrom = SecondaryAssignFrom?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringDoneBy = DoneBy?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> usersDetailsAssignTo = new List<string>();
                                List<string> usersDetailsAssignFrom = new List<string>();
                                List<string> usersDetailsRestored = new List<string>();
                                List<string> usersDetailsCreated = new List<string>();
                                List<string> usersDetailsLastModified = new List<string>();
                                List<string> usersDetailsLastDeleted = new List<string>();
                                List<string> usersDetailsHistoryAssignTo = new List<string>();
                                List<string> usersDetailsSecondaryAssignTo = new List<string>();
                                List<string> usersDetailsSecondaryAssignFrom = new List<string>();
                                List<string> usersDetailsDoneBy = new List<string>();
                                if (ConvertGuidToStringAssignTo.Count > 0)
                                {
                                    var userDetailsAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignTo, cancellationToken);
                                    usersDetailsAssignTo = userDetailsAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringLastDeleted.Count > 0)
                                {
                                    var userDetails = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringLastDeleted, cancellationToken);
                                    usersDetailsLastDeleted = userDetails.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringAssignFrom.Count > 0)
                                {
                                    var userDetailsAssignFrom = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignFrom, cancellationToken);
                                    usersDetailsAssignFrom = userDetailsAssignFrom.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringRestored.Count > 0)
                                {
                                    var userDetailsRestored = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringRestored, cancellationToken);
                                    usersDetailsRestored = userDetailsRestored.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringCreated.Count > 0)
                                {
                                    var userDetailsCreated = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringCreated, cancellationToken);
                                    usersDetailsCreated = userDetailsCreated.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringLastModified.Count > 0)
                                {
                                    var userDetailsLastModified = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringLastModified, cancellationToken);
                                    usersDetailsLastModified = userDetailsLastModified.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringHistoryAssignTo.Count > 0)
                                {
                                    var userDetailsHistoryAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringHistoryAssignTo, cancellationToken);
                                    usersDetailsHistoryAssignTo = userDetailsHistoryAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringSecondaryAssignTo.Count > 0)
                                {
                                    var userDetailsSecondaryAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringSecondaryAssignTo, cancellationToken);
                                    usersDetailsSecondaryAssignTo = userDetailsSecondaryAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringSecondaryAssignFrom.Count > 0)
                                {
                                    var userDetailsSecondaryAssignFrom = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringSecondaryAssignFrom, cancellationToken);
                                    usersDetailsSecondaryAssignFrom = userDetailsSecondaryAssignFrom.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringDoneBy.Count > 0)
                                {
                                    var userDetailsDoneBy = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringDoneBy, cancellationToken);
                                    usersDetailsDoneBy = userDetailsDoneBy.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                formattedFiltersDto.AssignTo = string.Join(", ", usersDetailsAssignTo);
                                formattedFiltersDto.AssignedFrom = string.Join(", ", usersDetailsAssignFrom);
                                formattedFiltersDto.CreatedBy = string.Join(", ", usersDetailsCreated);
                                formattedFiltersDto.LastModifiedBy = string.Join(", ", usersDetailsLastModified);
                                formattedFiltersDto.RestoredBy = string.Join(", ", usersDetailsRestored);
                                formattedFiltersDto.LastDeletedBy = string.Join(", ", usersDetailsLastDeleted);
                                formattedFiltersDto.SecondaryUsers = string.Join(", ", usersDetailsSecondaryAssignTo);
                                formattedFiltersDto.SecondaryFromIds = string.Join(", ", usersDetailsSecondaryAssignFrom);
                                formattedFiltersDto.HistoryAssignedToIds = string.Join(", ", usersDetailsHistoryAssignTo);
                                formattedFiltersDto.DoneBy = string.Join(", ", usersDetailsDoneBy);
                            }
                            if (StatusIds != null && StatusIds.Count > 0)
                            {
                                var displayNames = customLeadStatuses
                                    .Where(i => StatusIds.Contains(i?.MasterLeadStatusId ?? Guid.Empty) && i?.Level == 0)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.Status = string.Join(", ", displayNames);
                            }
                            if (SubStatusIds != null && SubStatusIds.Count > 0)
                            {
                                var displayNames = customLeadStatuses
                                    .Where(i => SubStatusIds.Contains(i?.MasterLeadStatusId ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.SubStatus = string.Join(", ", displayNames);
                            }

                            if (PropertyTypeId != null && PropertyTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => PropertyTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 0)
                                    .Select(status => status?.DisplayName)
                                    .ToList();
                                formattedFiltersDto.PropertyType = string.Join(", ", displayName);
                            }
                            if (PropertySubTypeId != null && PropertySubTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => PropertySubTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.PropertySubType = string.Join(", ", displayName);

                            }
                            List<Guid> subIds = new();
                            request.PageNumber = 1;
                            
                            try
                            {
                                if (request?.AssignTo?.Any() ?? false)
                                {
                                    if (request?.IsWithTeam ?? false)
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, input.TenantId ?? string.Empty)).ToList();
                                    }
                                    else
                                    {
                                        subIds = request?.AssignTo ?? new List<Guid>();
                                    }
                                }
                                else
                                {
                                   // subIds = (await _dapperRepository.GetSubordinateIdsAsync(input.CurrentUserId, input.TenantId ?? string.Empty, viewAllLeads:null, isAdmin))?.ToList() ?? new();
                                    if (request?.IsOnlyReportees ?? false)
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { input.CurrentUserId }, input.TenantId ?? string.Empty))?.ToList() ?? new();
                                    }
                                    else
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(input.CurrentUserId, input.TenantId ?? string.Empty, request?.CanAccessAllLeads))?.ToList() ?? new();
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "FunctionEntryPoint -> ExportLeadsHandler()",
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                                throw;
                            }
                            var tenant = input.TenantId;
                            //var leads = await _leadRepository.GetAllLeadsByNewFiltersForWebAsync(request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, input.CurrentUserId, new(), customLeadStatuses, tenant, isAdmin: isAdmin);
                            var leads = await _leadRepository.GetAllLeadsExportByNewFiltersForWebAsync(request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, input.CurrentUserId, new(), customLeadStatuses, tenant, isAdmin: isAdmin);
                            var ListofLeadIds = leads?.Select(i => i.Id).ToList();
                            List<NotesDetails> res = null;
                            List<ExportLeadFormattedDto> leadDtos = null;
                            int? notesCount = 0;
                            List<CommunicationDetails> communication = null;
                            try
                            {

                                var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<CommunicationDetails>("LeadratBlack", "GetCommunicationDetails", new
                                {
                                    leadids = ListofLeadIds,
                                    userid = isAdmin ? (Guid?)null : input.CurrentUserId,

                                });
                                communication = result.Adapt<List<CommunicationDetails>>();
                            }
                            catch
                            {
                            }
                            if (requestforFileName?.IsWithNotes == null || requestforFileName?.IsWithNotes == true)
                            {
                                try
                                {
                                    var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<NotesDetailsV1>("LeadratBlack", "GetAllNoteswithUpdatedDates", new
                                    {
                                        leadid = ListofLeadIds,
                                        tenantId = input.TenantId,
                                    });
                                    var notesDetails = ExportLeadHelper.GetAllDatawithNotes(result.ToList(), requestforFileName?.NotesCount ?? 3);
                                    res = notesDetails.Adapt<List<NotesDetails>>();
                                    leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, res.ToList(), requestforFileName?.IsWithNotes, globalSettings.ShouldRenameSiteVisitColumn, communication ?? new List<CommunicationDetails>(), requestforFileName?.IsWithFacebookProperties);
                                    notesCount = leadDtos.OrderByDescending(i => i.NotesDetails?.Count ?? 0)?.FirstOrDefault()?.NotesDetails?.Count();
                                }
                                catch (Exception ex)
                                {
                                    leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, null, false, shouldRenameSiteVisitColumn: globalSettings.ShouldRenameSiteVisitColumn, communication ?? new List<CommunicationDetails>(), requestforFileName?.IsWithFacebookProperties);
                                }

                            }
                            else
                            {
                                leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, null, requestforFileName.IsWithNotes, globalSettings.ShouldRenameSiteVisitColumn, communication ?? new List<CommunicationDetails>(), requestforFileName?.IsWithFacebookProperties);

                            }

                            var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                            var fileBytes = ExcelGeneration<ExportLeadFormattedDto>.GenerateExcel<ExportLeadFormattedDto, LeadFormettedFilterDto, ExportTrackerDto>(leadDtos, " Export Leads", formattedFiltersDto, tracker, notesCount ?? 0, requestforFileName?.IsWithFacebookProperties, requestforFileName.TimeZoneId,requestforFileName.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow));
                            var fileName = ExportLeadsHelper.ConvertToString(request.LeadVisibility) + "-" + request.FirstLevelFilter.ToString() + "-" + request.SecondLevelFilter.ToString();
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Leads/{input.TenantId ?? "Default"}", $"Export_Leads_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            List<string> toEmails = new();
                            List<string> ccEamils = new();
                            List<string> bccEamils = new();
                            if (exportTracker?.ToRecipients?.Any() ?? false)
                            {
                                toEmails.AddRange(exportTracker.ToRecipients);
                            }
                            if (exportTracker?.CcRecipients?.Any() ?? false)
                            {
                                ccEamils.AddRange(exportTracker.CcRecipients);
                            }
                            if (exportTracker?.BccRecipients?.Any() ?? false)
                            {
                                bccEamils.AddRange(exportTracker.BccRecipients);
                            }
                            var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                            emailSenderDto.To = toEmails;
                            emailSenderDto.Cc = ccEamils;
                            emailSenderDto.Bcc = bccEamils;
                            emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                            emailSenderDto.EmailBody = template;
                            emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                            emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                            await _graphEmailService.SendEmail(emailSenderDto);
                            isSent = true;
                            exportTracker.Count = leads.Count();
                            exportTracker.S3BucketKey = presignedUrl;
                            exportTracker.FileName = $"Export_Leads_" + requestforFileName.FileName + ".xlsx";
                            exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                            exportTracker.LastModifiedBy = input.CurrentUserId;
                            exportTracker.CreatedBy = input.CurrentUserId;
                            await _exportLeadRepo.UpdateAsync(exportTracker, cancellationToken);
                        }
                    }
                }

                catch (Exception ex)
                {
                    exportTracker.Message = ex.Message.ToString();
                    exportTracker.LastModifiedBy = input.CurrentUserId;
                    exportTracker.CreatedBy = input.CurrentUserId;
                    await _exportLeadRepo.UpdateAsync(exportTracker);
                    if (errorEmailTemplate != null && serviceProvider != null && !isSent)
                    {
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (exportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(exportTracker.ToRecipients);
                        }
                        if (exportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(exportTracker.CcRecipients);
                        }
                        if (exportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(exportTracker.BccRecipients);
                        }
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = errorEmailTemplate.Body;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FunctionEntryPoint -> ExportLeadsByNewFiltersHandler()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }
       
        public async Task ExportLeadsByCustomFiltersHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var globalSettings = (await _globalSettingsRepository.ListAsync(cancellationToken)).FirstOrDefault();
            if (globalSettings != null && globalSettings.IsLeadsExportEnabled)
            {
                ExportLeadTracker? exportTracker = await _exportLeadRepo.GetByIdAsync(input.TrackerId, cancellationToken);
                var serviceProvider = (await _masterEmailServiceProviderRepo.ListAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None)).FirstOrDefault();
                var errorEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ErrorMessage), CancellationToken.None)).FirstOrDefault();
                var exportEmailTemplate = (await _masterEmailTemplatesRepo.ListAsync(new GetMasterEmailTemplatesByEventSpec(Event.ExportLead), CancellationToken.None)).FirstOrDefault();
                EmailSenderDto emailSenderDto = new EmailSenderDto();
                bool isSent = false;
                Guid CreatredById = exportTracker.CreatedBy;
                string trackerIdString = CreatredById.ToString();
                ExportTrackerDto? tracker = exportTracker?.Adapt<ExportTrackerDto>();
                var userDetails1 = _userService.GetAsync(trackerIdString, cancellationToken);
                string firstName = userDetails1.Result.FirstName;
                string lastName = userDetails1.Result.LastName;
                string createdBy = $"{firstName} {lastName}";
                tracker.CreatedBy = createdBy;
                var exportTracker1 = new ExportTrackerDto
                {
                    CreatedBy = createdBy,
                };
                try
                {
                    if (exportTracker != null && serviceProvider != null && exportEmailTemplate != null)
                    {
                        #region Fetch all required MasterData and Other data
                        var users = new List<Lrb.Application.Identity.Users.UserDetailsDto>(await _userService.GetListAsync(cancellationToken));
                        var propetyTypes = new List<MasterPropertyType>(await _propertyTypeRepo.ListAsync(cancellationToken));
                        var customLeadStatuses = new List<CustomMasterLeadStatus>(await _customMastereadStatus.ListAsync(cancellationToken));
                        #endregion
                        GetAllLeadsByNewFiltersRequest? request = JsonConvert.DeserializeObject<GetAllLeadsByNewFiltersRequest>(exportTracker?.Request ?? string.Empty);
                        RunAWSBatchForExportLeadsByCustomFiltersRequest? requestforFileName = JsonConvert.DeserializeObject<RunAWSBatchForExportLeadsByCustomFiltersRequest>(exportTracker?.Request ?? string.Empty);
                        if (request != null)
                        {
                            LeadFilterDto? filtersDto = request.Adapt<LeadFilterDto>();
                            LeadFormettedFilterDto formattedFiltersDto = filtersDto.Adapt<LeadFormettedFilterDto>();
                            List<Guid> AssignTo = request.AssignTo;
                            List<Guid> AssignFrom = request.AssignedFromIds;
                            List<Guid> RestoredByIds = request.RestoredByIds;
                            List<Guid> CreatedByIds = request.CreatedByIds;
                            List<Guid> LastModifiedByIds = request.LastModifiedByIds;
                            List<Guid> LastDeletedBy = request.ArchivedByIds;
                            List<Guid> StatusIds = request.StatusIds;
                            List<Guid> SubStatusIds = request.SubStatusIds;
                            List<Guid> PropertyTypeId = request.PropertyType;
                            List<Guid> PropertySubTypeId = request.PropertySubType;
                            List<Guid> HistoryAssignTo = request.HistoryAssignedToIds;
                            List<Guid> SecondaryAssignTo = request.SecondaryUsers;
                            List<Guid> SecondaryAssignFrom = request.SecondaryFromIds;
                            List<Guid> DoneBy = request.DoneBy;
                            if ((AssignTo?.Count > 0) || (AssignFrom?.Count > 0) || (RestoredByIds?.Count > 0) || (CreatedByIds?.Count > 0) ||
                             (LastModifiedByIds?.Count > 0) || (LastDeletedBy?.Count > 0) || (SecondaryAssignTo?.Count > 0) || (SecondaryAssignFrom?.Count > 0)
                             || (HistoryAssignTo?.Count > 0) || (DoneBy?.Count > 0))
                            {
                                List<string> ConvertGuidToStringAssignTo = AssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringAssignFrom = AssignFrom?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringRestored = RestoredByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringCreated = CreatedByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringLastModified = LastModifiedByIds?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringLastDeleted = LastDeletedBy?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringHistoryAssignTo = HistoryAssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringSecondaryAssignTo = SecondaryAssignTo?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringSecondaryAssignFrom = SecondaryAssignFrom?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> ConvertGuidToStringDoneBy = DoneBy?.Select(guid => guid.ToString()).ToList() ?? new List<string>();
                                List<string> usersDetailsAssignTo = new List<string>();
                                List<string> usersDetailsAssignFrom = new List<string>();
                                List<string> usersDetailsRestored = new List<string>();
                                List<string> usersDetailsCreated = new List<string>();
                                List<string> usersDetailsLastModified = new List<string>();
                                List<string> usersDetailsHistoryAssignTo = new List<string>();
                                List<string> usersDetailsSecondaryAssignTo = new List<string>();
                                List<string> usersDetailsSecondaryAssignFrom = new List<string>();
                                List<string> usersDetailsDoneBy = new List<string>();

                                if (ConvertGuidToStringAssignTo.Count > 0)
                                {
                                    var userDetailsAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignTo, cancellationToken);
                                    usersDetailsAssignTo = userDetailsAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringLastDeleted.Count > 0)
                                {
                                    var userDetails = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignTo, cancellationToken);
                                    ConvertGuidToStringLastDeleted = userDetails.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringAssignFrom.Count > 0)
                                {
                                    var userDetailsAssignFrom = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringAssignFrom, cancellationToken);
                                    usersDetailsAssignFrom = userDetailsAssignFrom.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringRestored.Count > 0)
                                {
                                    var userDetailsRestored = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringRestored, cancellationToken);
                                    usersDetailsRestored = userDetailsRestored.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringCreated.Count > 0)
                                {
                                    var userDetailsCreated = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringCreated, cancellationToken);
                                    usersDetailsCreated = userDetailsCreated.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringLastModified.Count > 0)
                                {
                                    var userDetailsLastModified = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringLastModified, cancellationToken);
                                    usersDetailsLastModified = userDetailsLastModified.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringHistoryAssignTo.Count > 0)
                                {
                                    var userDetailsHistoryAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringHistoryAssignTo, cancellationToken);
                                    usersDetailsHistoryAssignTo = userDetailsHistoryAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringSecondaryAssignTo.Count > 0)
                                {
                                    var userDetailsSecondaryAssignTo = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringSecondaryAssignTo, cancellationToken);
                                    usersDetailsSecondaryAssignTo = userDetailsSecondaryAssignTo.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringSecondaryAssignFrom.Count > 0)
                                {
                                    var userDetailsSecondaryAssignFrom = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringSecondaryAssignFrom, cancellationToken);
                                    usersDetailsSecondaryAssignFrom = userDetailsSecondaryAssignFrom.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                if (ConvertGuidToStringDoneBy.Count > 0)
                                {
                                    var userDetailsDoneBy = await _userService.GetListOfUsersByIdsAsync(ConvertGuidToStringDoneBy, cancellationToken);
                                    usersDetailsDoneBy = userDetailsDoneBy.Select(user => $"{user.FirstName} {user.LastName}").ToList();
                                }
                                formattedFiltersDto.AssignTo = string.Join(", ", usersDetailsAssignTo);
                                formattedFiltersDto.AssignedFrom = string.Join(", ", usersDetailsAssignFrom);
                                formattedFiltersDto.CreatedBy = string.Join(", ", usersDetailsCreated);
                                formattedFiltersDto.LastModifiedBy = string.Join(", ", usersDetailsLastModified);
                                formattedFiltersDto.RestoredBy = string.Join(", ", usersDetailsRestored);
                                formattedFiltersDto.SecondaryUsers = string.Join(", ", usersDetailsSecondaryAssignTo);
                                formattedFiltersDto.SecondaryFromIds = string.Join(", ", usersDetailsSecondaryAssignFrom);
                                formattedFiltersDto.HistoryAssignedToIds = string.Join(", ", usersDetailsHistoryAssignTo);
                                formattedFiltersDto.DoneBy = string.Join(", ", usersDetailsDoneBy);
                            }
                            if (StatusIds != null && StatusIds.Count > 0)
                            {
                                var displayNames = customLeadStatuses
                                    .Where(i => StatusIds.Contains(i?.MasterLeadStatusId ?? Guid.Empty) && i?.Level == 0)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.Status = string.Join(", ", displayNames);
                            }
                            if (SubStatusIds != null && SubStatusIds.Count > 0)
                            {
                                var displayNames = customLeadStatuses
                                    .Where(i => SubStatusIds.Contains(i?.MasterLeadStatusId ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.SubStatus = string.Join(", ", displayNames);
                            }

                            if (PropertyTypeId != null && PropertyTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => PropertyTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 0)
                                    .Select(status => status?.DisplayName)
                                    .ToList();
                                formattedFiltersDto.PropertyType = string.Join(", ", displayName);
                            }
                            if (PropertySubTypeId != null && PropertySubTypeId.Count > 0)
                            {
                                var displayName = propetyTypes
                                    .Where(i => PropertySubTypeId.Contains(i?.Id ?? Guid.Empty) && i?.Level == 1)
                                    .Select(status => status?.DisplayName)
                                    .ToList();

                                formattedFiltersDto.PropertySubType = string.Join(", ", displayName);

                            }
                            List<Guid> subIds = new();
                            request.PageNumber = 1;
                            var isAdmin = await _dapperRepository.IsAdminV2Async(input.CurrentUserId, input.TenantId ?? string.Empty);
                            try
                            {
                                if (request?.AssignTo?.Any() ?? false)
                                {
                                    if (request?.IsWithTeam ?? false)
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignTo, input.TenantId ?? string.Empty)).ToList();
                                    }
                                    else
                                    {
                                        subIds = request?.AssignTo ?? new List<Guid>();
                                    }
                                }
                                else
                                {
                                    if (request?.IsOnlyReportees ?? false)
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { input.CurrentUserId }, input.TenantId ?? string.Empty))?.ToList() ?? new();
                                    }
                                    else
                                    {
                                        subIds = (await _dapperRepository.GetSubordinateIdsAsync(input.CurrentUserId, input.TenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                                    }
                                }
                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "FunctionEntryPoint -> ExportLeadsHandler()",
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                                throw;
                            }
                            CustomFilter? filter = null;
                            if (request.CustomFilterId == null || request.CustomFilterId == default)
                            {
                                filter = await _customFilterRepo.FirstOrDefaultAsync(new GetCustomFiltersSpec(input.CurrentUserId, isAdmin, true), cancellationToken);
                            }
                            else
                            {
                                filter = await _customFilterRepo.FirstOrDefaultAsync(new GetCustomFiltersSpec(request.CustomFilterId ?? Guid.Empty), cancellationToken);
                            }
                            var customStatus = await _customMastereadStatus.ListAsync(cancellationToken);
                            //var leads = await _leadRepository.GetAllLeadsByCustomFiltersForWebAsync(filter ?? new(), request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, input.CurrentUserId, isAdmin, new(), customMasterLeadStatus: customStatus);
                            var leads = await _leadRepository.GetAllLeadsExportByCustomFiltersForWebAsync(filter ?? new(), request.Adapt<GetAllLeadsByNewFiltersRequest>(), subIds, input.CurrentUserId, isAdmin, new(), customMasterLeadStatus: customLeadStatuses);
                            var ListofLeadIds = leads?.Select(i => i.Id).ToList();
                            List<NotesDetails> res = null;
                            List<ExportLeadFormattedDto> leadDtos = null;
                            List<CommunicationDetails> communication = null;
                            try
                            {
                                var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<CommunicationDetails>("LeadratBlack", "GetCommunicationDetails", new
                                {
                                    leadids = ListofLeadIds,
                                    userid = isAdmin ? (Guid?)null : input.CurrentUserId,

                                });
                                communication = result.Adapt<List<CommunicationDetails>>();
                            }
                            catch
                            {
                            }
                            int? notesCount = 0;
                            if (requestforFileName.IsWithNotes == null || requestforFileName.IsWithNotes == true)
                            {
                                try
                                {
                                    var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<NotesDetailsV1>("LeadratBlack", "GetAllNoteswithUpdatedDates", new
                                    {
                                        leadid = ListofLeadIds,
                                        tenantId = input.TenantId,
                                    });
                                    var notesDetails = ExportLeadHelper.GetAllDatawithNotes(result.ToList(), requestforFileName?.NotesCount ?? 3);
                                    res = notesDetails.Adapt<List<NotesDetails>>();
                                    leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, res.ToList(), requestforFileName?.IsWithNotes,shouldRenameSiteVisitColumn:globalSettings.ShouldRenameSiteVisitColumn, isWithFacebookProperties :requestforFileName?.IsWithFacebookProperties);
                                    notesCount = leadDtos.OrderByDescending(i => i.NotesDetails?.Count ?? 0)?.FirstOrDefault()?.NotesDetails?.Count();
                                }
                                catch
                                {
                                    leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, null, false,shouldRenameSiteVisitColumn:globalSettings.ShouldRenameSiteVisitColumn, isWithFacebookProperties: requestforFileName?.IsWithFacebookProperties);
                                }

                            }
                            else
                            {
                                leadDtos = ExportLeadHelper.ConvertToLeadDto(leads.ToList(), users, customLeadStatuses, propetyTypes, null, requestforFileName.IsWithNotes, shouldRenameSiteVisitColumn: globalSettings.ShouldRenameSiteVisitColumn, isWithFacebookProperties :requestforFileName?.IsWithFacebookProperties);
                            }

                            var exportTemplate = await _exportTemplateRepo.GetByIdAsync(exportTracker?.TemplateId ?? Guid.Empty);
                            var fileBytes = ExcelGeneration<ExportLeadFormattedDto>.GenerateExcel<ExportLeadFormattedDto, LeadFormettedFilterDto, ExportTrackerDto>(leadDtos, " Export Leads", formattedFiltersDto, tracker, notesCount ?? 0, requestforFileName?.IsWithFacebookProperties, requestforFileName.TimeZoneId, requestforFileName.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow));
                            var fileName = ExportLeadsHelper.ConvertToString(request.LeadVisibility) + "-" + request.FirstLevelFilter.ToString() + "-" + request.SecondLevelFilter.ToString();
                            var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Leads/{input.TenantId ?? "Default"}", $"Export_Leads_" + input.TenantId + requestforFileName.FileName + ".xlsx", fileBytes, 0);
                            var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                            List<string> toEmails = new();
                            List<string> ccEamils = new();
                            List<string> bccEamils = new();
                            if (exportTracker?.ToRecipients?.Any() ?? false)
                            {
                                toEmails.AddRange(exportTracker.ToRecipients);
                            }
                            if (exportTracker?.CcRecipients?.Any() ?? false)
                            {
                                ccEamils.AddRange(exportTracker.CcRecipients);
                            }
                            if (exportTracker?.BccRecipients?.Any() ?? false)
                            {
                                bccEamils.AddRange(exportTracker.BccRecipients);
                            }
                            var template = ExportLeadHelper.ReplaceVariables(exportEmailTemplate?.Body ?? string.Empty, new Dictionary<string, string>() { { string.Format("#PresignedUrl#"), presignedUrl } });
                            emailSenderDto.To = toEmails;
                            emailSenderDto.Cc = ccEamils;
                            emailSenderDto.Bcc = bccEamils;
                            emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                            emailSenderDto.EmailBody = template;
                            emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                            emailSenderDto.Subject = exportEmailTemplate?.Subject ?? string.Empty;
                            await _graphEmailService.SendEmail(emailSenderDto);
                            isSent = true;
                            exportTracker.Count = leads.Count();
                            exportTracker.S3BucketKey = presignedUrl;
                            exportTracker.FileName = $"Export_Leads_" + requestforFileName.FileName + ".xlsx";
                            exportTracker.Template = JsonConvert.SerializeObject(exportTemplate);
                            exportTracker.LastModifiedBy = input.CurrentUserId;
                            exportTracker.CreatedBy = input.CurrentUserId;
                            await _exportLeadRepo.UpdateAsync(exportTracker, cancellationToken);
                        }
                    }
                }

                catch (Exception ex)
                {
                    exportTracker.Message = ex.Message.ToString();
                    exportTracker.LastModifiedBy = input.CurrentUserId;
                    exportTracker.CreatedBy = input.CurrentUserId;
                    await _exportLeadRepo.UpdateAsync(exportTracker);
                    if (errorEmailTemplate != null && serviceProvider != null && !isSent)
                    {
                        List<string> toEmails = new();
                        List<string> ccEamils = new();
                        List<string> bccEamils = new();
                        if (exportTracker?.ToRecipients?.Any() ?? false)
                        {
                            toEmails.AddRange(exportTracker.ToRecipients);
                        }
                        if (exportTracker?.CcRecipients?.Any() ?? false)
                        {
                            ccEamils.AddRange(exportTracker.CcRecipients);
                        }
                        if (exportTracker?.BccRecipients?.Any() ?? false)
                        {
                            bccEamils.AddRange(exportTracker.BccRecipients);
                        }
                        emailSenderDto.To = toEmails;
                        emailSenderDto.Cc = ccEamils;
                        emailSenderDto.Bcc = bccEamils;
                        emailSenderDto.BodyType = Microsoft.Graph.BodyType.Html;
                        emailSenderDto.EmailBody = errorEmailTemplate.Body;
                        emailSenderDto.SenderEmailAddress = serviceProvider?.SenderEmailAddress ?? string.Empty;
                        emailSenderDto.Subject = errorEmailTemplate?.Subject ?? string.Empty;
                        await _graphEmailService.SendEmail(emailSenderDto);
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "FunctionEntryPoint -> ExportLeadsByNewFiltersHandler()",
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
        }
    }
}
