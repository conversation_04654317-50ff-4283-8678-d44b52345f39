﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Domain.Entities.CustomAddress;
using Newtonsoft.Json;
using System.Globalization;

namespace Lrb.Application.CustomAddress.Mobile
{
    public class GetCustomAndGoogleLocationAddressesRequest : PaginationFilter, IRequest<PagedResponse<CustomAddressDto, string>>
    {
        public string? SearchText { get; set; }
    }
    public class GetCustomAndGoogleLocationAddressesRequestHandler : IRequestHandler<GetCustomAndGoogleLocationAddressesRequest, PagedResponse<CustomAddressDto, string>>
    {
        private readonly IReadRepository<CustomAddressDirectory> _locationRepo;
        private readonly IReadRepository<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        protected readonly IGooglePlacesService _googlePlacesService;

        public GetCustomAndGoogleLocationAddressesRequestHandler(IReadRepository<CustomAddressDirectory> locationRepo,
            IReadRepository<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IGooglePlacesService googlePlacesService)
        {
            _locationRepo = locationRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _googlePlacesService = googlePlacesService;
        }
        public async Task<PagedResponse<CustomAddressDto, string>> Handle(GetCustomAndGoogleLocationAddressesRequest request, CancellationToken cancellationToken)
        {
            var storedLocationDtos = new List<CustomAddressDto>();
            List<AutocompleteLocationModel> locationModels = new();
            string? countryName = null;
            var globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);

            var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);

            if (globalSettings != null && countries != null)
            {
                foreach (var country in countries)
                {
                    countryName = new RegionInfo(country.Code ?? "IN").EnglishName;
                }
            }
            var locations = await _locationRepo.ListAsync(new GetAllCustomAddressesSpec(request.SearchText), cancellationToken);
            storedLocationDtos.AddRange(locations?.Adapt<List<CustomAddressDto>>() ?? new());
            if (request.SearchText != null)
            {
                locationModels = await _googlePlacesService.GetPlacesAutocompleteResultAsync(request.SearchText ?? string.Empty, countryName);
                storedLocationDtos.AddRange(locationModels?.Adapt<List<CustomAddressDto>>() ?? new());

            }
            return new(storedLocationDtos.Skip((request.PageNumber - 1) * request.PageSize).Take(request.PageSize), storedLocationDtos.Count);
        }
    }

}
