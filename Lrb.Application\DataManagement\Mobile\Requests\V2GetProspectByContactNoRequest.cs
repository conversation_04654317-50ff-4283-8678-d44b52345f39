﻿using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.GlobalSettings.Mobile;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class V2GetProspectByContactNoRequest : IRequest<Response<ProspectContactDto>>
    {
        public string ContactNo {  get; set; }
        public string CountryCode { get; set; }
        public V2GetProspectByContactNoRequest(string? contactNo,string countryCode)
        {
            ContactNo = contactNo;
            CountryCode = countryCode;
        }

    }
    public class V2GetProspectByContactNoRequestHandler : IRequestHandler<V2GetProspectByContactNoRequest, Response<ProspectContactDto>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly ICurrentUser _currentUser; 
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        public V2GetProspectByContactNoRequestHandler(IRepositoryWithEvents<Prospect> prospectRepo, ICurrentUser currentUser, IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo)
        {
            _prospectRepo = prospectRepo;
            _currentUser = currentUser;
            _globalSettingsRepo = globalSettingsRepo;
        }

        public async Task<Response<ProspectContactDto>> Handle(V2GetProspectByContactNoRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.ContactNo)) { throw new Exception("Contact No field can not be empty."); }
            if (string.IsNullOrWhiteSpace(request?.CountryCode?.Trim()))
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);
                request.CountryCode= countries.FirstOrDefault().DefaultCallingCode;

            }
            if (string.IsNullOrEmpty(request.CountryCode)) { throw new Exception("Country Code field can not be empty."); }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            string rawContactNo = request.ContactNo.Trim();
            var prospect = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByContactNoSpecs(request.ContactNo,request.CountryCode));
            if (prospect != null)
            {
                return new(new ProspectContactDto() { Id = prospect.Id, CanNavigate = true });
            }
            return new(null);
        }
    }
}
