namespace Lrb.Application.Identity.Tokens;

public interface ITokenService : ITransientService
{
    Task<CognitoTokenResponse> GetTokenAsync(TokenRequest request, string ipAddress, CancellationToken cancellationToken);

    Task<CognitoTokenResponse> RefreshTokenAsync(RefreshTokenRequest request, string ipAddress);

    Task<bool> ResetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken);

    Task<AccountLockoutInfo> GetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken);
}
public interface IMobileTokenService : ITransientService
{
    Task<CognitoTokenResponse> GetTokenAsync(TokenRequest request, string ipAddress, CancellationToken cancellationToken);

    Task<CognitoTokenResponse> RefreshTokenAsync(RefreshTokenRequest request, string ipAddress);

    Task<bool> ResetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken);

    Task<AccountLockoutInfo> GetFailedLoginAttemptsAsync(string userName, CancellationToken cancellationToken);
}