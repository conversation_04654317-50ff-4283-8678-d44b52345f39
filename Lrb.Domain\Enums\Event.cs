﻿namespace Lrb.Domain.Enums
{
    public enum Event
    {   None = -1,
        SignUp = 0,
        <PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        LeadStatusToCallback,
        CallbackReminder,
        LeadStatusToScheduleMeeting,
        ScheduleMeetingReminder,
        LeadStatusToScheduleSiteVisit,
        ScheduleSiteVisitReminder,
        LeadStatusToBook,
        LeadStatusToNotInterest,
        LeadSatusToDropped,
        LeadStatusToPending,
        SingleTaskComplete,
        MultipleTaskComplete,
        TaskCreation,
        TaskUpdation,
        ScheduledTaskReminder,
        TaskOverdue,
        TaskPending,
        LeadFromPropertyMicrositeToBuy,
        LeadFromPropertyMicrositeForRent,
        LeadFromPortfolioMicrosite,
        IntegrationAccCreation,
        LeadFromIntegration,
        PropertyCreation,
        PropertyDeletion,
        PropertySoftDeletion,
        DusKaDumChallengeStarts,
        SupportQuery,
        LeadAssignment,
        TaskAssignment,
        UnAssignedLeadUpdate,
        MultipleLeadAssignment,
        DailyMorningUpdates,
        DailyEveningUpdates,
        LeadInfoUpdate,
        ArchiveLead,
        RestoreLead,
        LeadSiteVisitDone,
        LeadSiteVisitNotDone,
        LeadMeetingDone,
        LeadMeetingNotDone,
        DailyPushNotificationNewToPendingUpdateAdmin,
        DailyPushNotificationNewToPendingUpdateUser,
        ExportLead,
        ErrorMessage,
        FacebookErrorMessage,
        FacebookBulkLeadsFetch,
        DuplicateUnAssigment,
        GreetingsForTest,
        LeadInfoForEmailNotification,
        NotifiyManagerAndAdmins,
        LeadMovedToUnassigned,
        DuplicateLeadEnquiryAlert,
        SubscriptionThreshold,
        LeadRotation,
        ExportTeam,
        SendPendingPaymentDueMail,
        LeadReAssignment,
        MultipleLeadAssignmentNotificationForAll,
        SendIntegrationEmail,
        Engegato,
        LeadRotationPreAlert,
        LeadRotationPostAlert,
        ScheduleReferralReminder,
        LeadStatusToScheduleReferral, 
        LeadReferralDone,
        LeadReferralNotDone,
        WhatsappNewMessage
    }
}
