﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.Identity;
using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Utils;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PhoneNumbers;
using RestSharp;
using System.Text;
using System.Text.RegularExpressions;

namespace Lrb.Application.Integration.Web.Helpers
{
    public static class FacebookLeadHelper
    {

        public static Domain.Entities.Lead? MapToLead(this FacebookLeadDto? fbLeadDto, IDictionary<DataColumns, string>? mappingDetails)
        {
            Domain.Entities.Lead lead = new();
            var fieldData = fbLeadDto?.FieldData;
            if (fieldData != null && mappingDetails?.Count > 0)
            {
                var contactNo = string.Empty;
                mappingDetails.TryGetValue(DataColumns.ContactNo, out contactNo);
                lead.ContactNo = fieldData?.FirstOrDefault(i => i.Name == contactNo)?.Values?.FirstOrDefault() ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(lead.ContactNo) && lead.ContactNo.Length >= 10 && Regex.IsMatch(lead.ContactNo, RegexPatterns.MobNoPattern))
                {
                    lead.ContactNo = lead.ContactNo;

                    var name = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.Name, out name);
                    lead.Name = string.IsNullOrWhiteSpace((fieldData ?? new()).GetLeadName()) ? "Facebook Enquiry" : (fieldData ?? new()).GetLeadName();

                    var email = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.Email, out email);
                    lead.Email = fieldData?.FirstOrDefault(i => i.Name == email)?.Values?.FirstOrDefault() ?? string.Empty;

                    var rating = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.Rating, out rating);
                    lead.Rating = fieldData?.FirstOrDefault(i => i.Name == rating)?.Values?.FirstOrDefault() ?? string.Empty;

                    var budget = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.Budget, out budget);
                    var budgetString = fieldData?.FirstOrDefault(i => i.Name == budget)?.Values?.FirstOrDefault() ?? string.Empty;
                    lead.Notes = budgetString;

                    LeadEnquiry enquiry = new();
                    enquiry.LowerBudget = enquiry.UpperBudget = ExtractBudget(budgetString);

                    enquiry.Address = new();
                    var city = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.City, out city);
                    enquiry.Address.City = fieldData?.FirstOrDefault(i => i.Name == city)?.Values?.FirstOrDefault() ?? string.Empty;
                    enquiry.Address.Locality = fieldData?.FirstOrDefault(i => i.Name == city)?.Values?.FirstOrDefault() ?? string.Empty;

                    var state = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.State, out state);
                    enquiry.Address.State = fieldData?.FirstOrDefault(i => i.Name == state)?.Values?.FirstOrDefault() ?? string.Empty;

                    var location = string.Empty;
                    mappingDetails.TryGetValue(DataColumns.Location, out location);
                    enquiry.Address.SubLocality = fieldData?.FirstOrDefault(i => i.Name == location)?.Values?.FirstOrDefault() ?? string.Empty;

                    enquiry.LeadSource = fbLeadDto?.Platform == "ig" ? LeadSource.Instagram : LeadSource.Facebook;
                    enquiry.IsPrimary = true;
                    lead.Enquiries = new List<LeadEnquiry>
                    {
                        enquiry
                    };
                    return lead;
                }
                else
                {
                    return null;
                }

            }
            return null;
        }
        private static long ExtractBudget(string budgetString)
        {
            var resultString = Regex.Match(budgetString, @"\d+").Value;
            if (!string.IsNullOrWhiteSpace(resultString))
            {
                var lakhsNotations = new List<string> { "lakhs", "lakh", "lkh", "lak", "lk", "lack", "luck", "luk", "luks", "lukh", "lukhs", "lks" };
                var hazarNotations = new List<string> { "thousands", "thousand", "thsnd", "thosand", "thausand", "thusands", "hazar", "hajar", "hjr", "hazr", "hajr", "hzr", "savira", "savra", "svra", };
                var croreNotations = new List<string> { "crore", "cr", "crre", "koti", "coti", "crr" };

                if (lakhsNotations.Any(budgetString.ToLowerInvariant().Contains))
                {
                    return Convert.ToInt64(resultString) * 100000;
                }
                if (croreNotations.Any(budgetString.ToLowerInvariant().Contains))
                {
                    return Convert.ToInt64(resultString) * 10000000;
                }
                if (hazarNotations.Any(budgetString.ToLowerInvariant().Contains))
                {
                    return Convert.ToInt64(resultString) * 1000;
                }
                else
                {
                    return Convert.ToInt64(resultString);
                }
            }
            else
            {
                return 0;
            }
        }

        public static Domain.Entities.Lead? MapToGenericLead(this FacebookLeadDto? fbLeadDto, FacebookAdsInfo? ad, FacebookLeadGenForm? form, bool isInstagramSourceEnabled = false, Domain.Entities.GlobalSettings globalsettings = null, string? leadgenId=null, int? createdTime=null) 
        {
            const string _name = "name";
            const string _email = "email";
            const string _leadPhone = "lead_phone_number";
            const string _phone = "phone_number";
            const string _city = "city";
            var nameCombinations = new List<string>() { "fullname", "full_name", "full name", "yourname", "your_name", "your name", "buyer's name", "buyer_name", "buyer", "buyer name" };
            var nameCombinaitonsToSkip = new List<string>() { "project_name", "project_name", "project name", "propertyname", "property_name", "property name", "site name", "location name", "locationname", "location_name" };
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalsettings?.CountriesInfo);
            string? currency = countries?.FirstOrDefault()?.DefaultCurrency;
            Domain.Entities.Lead lead = new();
            var fieldData = fbLeadDto?.FieldData;
            if (fieldData != null)
            {
                var contactNo = string.Empty;
                try
                {
                    if(!string.IsNullOrEmpty(fbLeadDto?.Id))
                    {
                        lead.MetaLeadId = fbLeadDto?.Id;
                    }
                    var input = ExtractFacebookContactNumber(fieldData);
                    //  var  contactnumber = ValidateContactNumbers(input ?? string.Empty, globalsettings);
                    var contactnumberTask = ValidateContactNumbers(input ?? string.Empty, globalsettings, fbLeadDto?.CountryCode);
                    var contactnumber = contactnumberTask.GetAwaiter().GetResult();


                    if (string.IsNullOrEmpty(input))
                    {
                        input = fieldData?.FirstOrDefault(i => !(i.Name?.ToLower()?.Contains("budget") ?? false) && !(i.Name?.ToLower()?.Contains("cost") ?? false) && !(i.Name?.ToLower()?.Contains("price") ?? false) && i.Values != null && i.Values.Any(j => Regex.Match(j ?? string.Empty, @"[0-9]+$").Value.Length >= 10))?.Values?[0];
                        //input = fieldData?.FirstOrDefault(i => i.Values?.Any(i => (Regex.Replace(i, @"[\(\) -]", "")?.Length > 10) && (Regex.Replace(i, @"[\(\) -]", "")?.Substring(input.Length - 10)?.All(char.IsDigit) ?? false)) ?? false).values?.FirstOrDefault();
                    }
                    if (!string.IsNullOrWhiteSpace(input))
                    {
                        lead.ContactNo = contactnumber;
                        lead.CountryCode = fbLeadDto?.CountryCode;
                    }
                }
                catch { }
                if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                {
                    try
                    {
                        lead.AlternateContactNo = fieldData?.FirstOrDefault(i => i.Name?.ToLowerInvariant() == "work_phone_number")?.Values?.FirstOrDefault();
                        if (!string.IsNullOrEmpty(lead.AlternateContactNo))
                        {
                            lead.AlternateContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(null, lead.AlternateContactNo, globalsettings, ad?.CountryCode ?? fbLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91");
                            if (lead.ContactNo == lead.AlternateContactNo)
                            {
                                lead.AlternateContactNo = null;
                                lead.AltCountryCode = fbLeadDto?.CountryCode;
                            }
                        }
                    }
                    catch { }
                    try
                    {
                        lead.CreatedOnPortal = fbLeadDto?.CreatedTime.ConvertAndSetKindAsUtc();
                    }
                    catch { }
                    try
                    {
                        lead.Name = string.IsNullOrWhiteSpace((fieldData ?? new()).GetLeadName()) ? "Facebook Enquiry" : (fieldData ?? new()).GetLeadName();
                    }
                    catch { }

                    try
                    {
                        lead.Email = fieldData?.FirstOrDefault(i => i.Name == _email || (i.Name?.ToLower()?.Contains(_email) ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                    }
                    catch { }


                    //lead.Notes = JsonConvert.SerializeObject(fieldData, Formatting.Indented);

                    try
                    {
                        List<string> fieldsToAvoid = new()
                        {
                            _name, _email, _phone, _leadPhone
                        };
                        if (fieldData?.Any() ?? false)
                        {
                            lead.Notes = string.Join(", \n", fieldData.Select(i => !string.IsNullOrEmpty(i.Name) && !fieldsToAvoid.Contains(i.Name) ? i.Name + " - " + string.Join(',', i?.Values ?? new List<string>()) : null).Where(i => i != null));
                            //.Select(i => i.Name + " - " + string.Join(',', i?.Values ?? new List<string>()));
                            //lead.Notes = string.Join(" ",additionalFields);
                        }
                    }
                    catch { }
                    if (ad != null)
                    {
                        lead.ConfidentialNotes += Environment.NewLine + $"Ad Name - {ad.AdName},\n Ad Set Name - {ad.AdSetName},\n campaign name - {ad.CampaignName},\n Ad Accountname {ad.AdAccountName}";
                    }
                    if (form != null)
                    {
                        lead.ConfidentialNotes += Environment.NewLine + $"Form Name - {form.Name},\n Form Id - {form?.FacebookId}";
                    }
                    var address = new Address();
                    address.City = fieldData?.FirstOrDefault(i => i.Name == _city || (i.Name?.ToLower()?.Contains(_city) ?? false))?.Values?.FirstOrDefault() ?? string.Empty;

                    LeadEnquiry enquiry = new();
                    if (!string.IsNullOrWhiteSpace(address.City))
                    {
                        enquiry.Addresses = new List<Address> { address };
                    }
                    enquiry.LeadSource = LeadSource.Facebook;
                    enquiry.Currency = currency ?? "INR";
                    if (isInstagramSourceEnabled)
                    {
                        enquiry.LeadSource = fbLeadDto?.Platform == "ig" ? LeadSource.Instagram : LeadSource.Facebook;
                    }
                    enquiry.SubSource = ad != null ? $"{ad.AdName} (XX{ad.AdId?[^3..]})".ToLower() : form != null ? $"{form?.Name} (XX{form?.FacebookId?[^3..]})".ToLower() : string.Empty;
                    enquiry.IsPrimary = true;
                    lead.Enquiries = new List<LeadEnquiry>
                    {
                        enquiry
                    };
                    try
                    {
                        var ads = ad.Adapt<ExtractFacebookAdInfoDto>();
                        var forms = form.Adapt<ExtractFaceBookFormInfoDto>();
                        if (lead.AdditionalProperties == null)
                        {
                            lead.AdditionalProperties = new Dictionary<string, string>();
                        }
                        if (!string.IsNullOrWhiteSpace(leadgenId))
                        {
                            lead.AdditionalProperties.Add("LeadgenId", leadgenId);
                        }
                        if(createdTime != null && createdTime != 0)
                        {
                            lead.AdditionalProperties.Add("CreatedTime", createdTime?.ToString());
                        }
                        var adsProperties = ExtractAdditionalProperties(ads);
                        foreach (var property in adsProperties)
                        {
                            lead.AdditionalProperties[property.Key] = property.Value;
                        }
                        var formsProperties = ExtractAdditionalProperties(forms);
                        foreach (var property in formsProperties)
                        {
                            lead.AdditionalProperties[property.Key] = property.Value;
                        }
                    }
                    catch { }
                    return lead;
                }
                else
                {
                    return null;
                }

            }
            return null;
        }
        public static IDictionary<string, string> ExtractAdditionalProperties(object source)
        {
            var additionalProperties = new Dictionary<string, string>();

            if (source == null) return additionalProperties;  

            var properties = source.GetType().GetProperties();

            foreach (var property in properties)
            {
                string propertyName = property.Name;
                var propertyValue = property.GetValue(source);

                if (propertyValue == null)
                {
                    additionalProperties[propertyName] = string.Empty;
                }
                else
                {
                    additionalProperties[propertyName] = propertyValue.ToString();
                }
            }

            return additionalProperties; 
        }


        private static async Task<string> ValidateContactNumbers(string contactNos, Domain.Entities.GlobalSettings globalSetting, string countryCodes)
        {
            string contactNo = Regex.Replace(contactNos, "[^0-9]", "");
            string defaultRegion = string.Empty;
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSetting?.CountriesInfo);
            var countryCode = countryCodes ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

            if (contactNos.StartsWith("0") && contactNos.Length > 6 && contactNos.Length < 20)
            {
                contactNo = "+91" + contactNo.Substring(1);
                return contactNo;
            }
            if (contactNos.StartsWith("+"))
            {
                PhoneNumber number = phoneUtil.Parse("+" + contactNo, null);
                defaultRegion = phoneUtil.GetRegionCodeForNumber(number);

            }
            if (string.IsNullOrWhiteSpace(defaultRegion))
            {
                string Code = Regex.Replace(countryCodes, "[^0-9]", "");
                int country = int.Parse(Code);
                List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(country, new List<string>());
                defaultRegion = regionCodes.FirstOrDefault();
                if (string.IsNullOrWhiteSpace(defaultRegion))
                {
                    defaultRegion = countries?.FirstOrDefault()?.Code;
                }
            }

            PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);
            PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
            string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
            string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
            string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
            bool isValid;
            if (defaultRegion == "AE")
            {
                if (contactWithCountryCode.Length == 12)
                {
                    return contactWithCountryCode;

                }
            }
            if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
            {
                return contactWithCountryCode;
            }
            else if (contactNos.StartsWith("+") && contactNos.Length > 6 && contactNos.Length < 20)
            {
                return "+" + contactNo;
            }
            else if (contactNos.StartsWith("0") && contactNos.Length > 6 && contactNos.Length < 20)
            {
                return contactNo;
            }
            else if ((string.IsNullOrWhiteSpace(countryCodes) && contactNos.Length > 6 && contactNos.Length < 20))
            {
                return contactNo;
            }

            else if (contactNos.Length > 6 && contactNos.Length < 20)
            {
                return countryCode + contactNo;

            }
            else
            {
                return string.Empty;
            }
        }

        public static List<Domain.Entities.Lead> MapToGenericLeads(this IEnumerable<FacebookLeadDto?>? fbLeadDtos, FacebookAdsInfo? ad = null, FacebookLeadGenForm? form = null, bool isInstagramSourceEnabled = false, Domain.Entities.GlobalSettings globalSettings = null)
        {
            const string _name = "name";
            const string _email = "email";
            const string _leadPhone = "lead_phone_number";
            const string _phone = "phone_number";
            const string _city = "city";
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);
            List<Domain.Entities.Lead> leads = new();
            if (fbLeadDtos != null)
            {
                foreach (var fbLeadDto in fbLeadDtos)
                {
                    Domain.Entities.Lead lead = new();
                    var fieldData = fbLeadDto?.FieldData;
                    if (fieldData != null)
                    {
                        var contactNo = string.Empty;
                        try
                        {
                            var input = ExtractFacebookContactNumber(fieldData);
                            var contactnumberTask = ValidateContactNumbers(input ?? string.Empty, globalSettings, ad?.CountryCode ?? fbLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91");
                            var contactnumber = contactnumberTask.GetAwaiter().GetResult();
                            if (string.IsNullOrEmpty(input))
                            {
                                input = fieldData?.FirstOrDefault(i => !(i.Name?.ToLower()?.Contains("budget") ?? false) && !(i.Name?.ToLower()?.Contains("cost") ?? false) && !(i.Name?.ToLower()?.Contains("price") ?? false) && i.Values != null && i.Values.Any(j => Regex.Match(j ?? string.Empty, @"[0-9]+$").Value.Length >= 10))?.Values?[0];
                                //input = fieldData?.FirstOrDefault(i => i.Values?.Any(i => (Regex.Replace(i, @"[\(\) -]", "")?.Length > 10) && (Regex.Replace(i, @"[\(\) -]", "")?.Substring(input.Length - 10)?.All(char.IsDigit) ?? false)) ?? false).values?.FirstOrDefault();
                            }
                            if (!string.IsNullOrWhiteSpace(input))
                            {
                                lead.ContactNo = contactnumber;
                                lead.CountryCode = ad?.CountryCode ?? fbLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";

                            }
                            else
                            {
                                lead.ContactNo = string.Empty;
                            }
                        }
                        catch { }
                        if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                        {
                            try
                            {
                                lead.AlternateContactNo = fieldData?.FirstOrDefault(i => i.Name?.ToLowerInvariant() == "work_phone_number")?.Values?.FirstOrDefault();
                                if (!string.IsNullOrEmpty(lead.AlternateContactNo))
                                {
                                    lead.AlternateContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(null, lead.AlternateContactNo, globalSettings, ad?.CountryCode ?? fbLeadDto?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91");
                                    if (lead.ContactNo == lead.AlternateContactNo)
                                    {
                                        lead.AlternateContactNo = null;
                                        lead.AltCountryCode = fbLeadDto?.CountryCode;

                                    }
                                }
                            }
                            catch { }
                            try
                            {
                                lead.CreatedOnPortal = fbLeadDto?.CreatedTime.ConvertAndSetKindAsUtc();
                            }
                            catch { }
                            try
                            {
                                lead.Name = string.IsNullOrWhiteSpace((fieldData ?? new()).GetLeadName()) ? "Facebook Enquiry" : (fieldData ?? new()).GetLeadName();
                            }
                            catch { }

                            try
                            {
                                lead.Email = fieldData?.FirstOrDefault(i => i.Name == _email || (i.Name?.ToLower()?.Contains(_email) ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                            }
                            catch { }

                            //lead.Notes = JsonConvert.SerializeObject(fieldData, Formatting.Indented);

                            try
                            {
                                List<string> fieldsToAvoid = new()
                                {
                                    _name, _email, _phone, _leadPhone
                                };
                                if (fieldData?.Any() ?? false)
                                {
                                    lead.Notes = string.Join(", \n", fieldData.Select(i => !string.IsNullOrEmpty(i.Name) && !fieldsToAvoid.Contains(i.Name) ? i.Name + " - " + string.Join(',', i?.Values ?? new List<string>()) : null).Where(i => i != null));
                                    //.Select(i => i.Name + " - " + string.Join(',', i?.Values ?? new List<string>()));
                                    //lead.Notes = string.Join(" ",additionalFields);
                                }
                            }
                            catch { }
                            if (ad != null)
                            {
                                lead.ConfidentialNotes += Environment.NewLine + $"Ad Name - {ad.AdName},\n Ad Set Name - {ad.AdSetName},\n campaign name - {ad.CampaignName},\n Ad Accountname {ad.AdAccountName}";
                            }
                            if (form != null)
                            {
                                lead.ConfidentialNotes += Environment.NewLine + $"Form Name - {form.Name},\n Form Id - {form?.FacebookId}";
                            }

                            var address = new Address();
                            address.City = fieldData?.FirstOrDefault(i => i.Name == _city || (i.Name?.ToLower()?.Contains(_city) ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                            LeadEnquiry enquiry = new();

                            if (!string.IsNullOrWhiteSpace(address.City))
                            {
                                enquiry.Addresses = new List<Address> { address };
                            }
                            enquiry.LeadSource = LeadSource.Facebook;
                            if (isInstagramSourceEnabled)
                            {
                                enquiry.LeadSource = fbLeadDto?.Platform == "ig" ? LeadSource.Instagram : LeadSource.Facebook;
                            }
                            enquiry.SubSource = ad != null ? $"{ad.AdName} (XX{ad.AdId?[^3..]})" : form != null ? $"{form?.Name} (XX{form?.FacebookId?[^3..]})" : string.Empty;
                            enquiry.IsPrimary = true;
                            lead.Enquiries = new List<LeadEnquiry>
                        {
                            enquiry
                        };
                            leads.Add(lead);
                        }
                    }
                }
            }
            return leads.Where(i => !string.IsNullOrWhiteSpace(i.ContactNo)).ToList();
        }
        public static List<ExportFacebookBulkLeadsDto> MapToExportDtos(this IEnumerable<FacebookLeadDto> fbLeadDtos)
        {
            const string _leadPhone = "lead_phone_number";
            const string _phone = "phone_number";
            List<ExportFacebookBulkLeadsDto> result = new();
            foreach (var fbLeadDto in fbLeadDtos)
            {
                ExportFacebookBulkLeadsDto dto = new();
                var fieldData = fbLeadDto?.FieldData;
                if (fieldData != null)
                {
                    var contactNo = string.Empty;
                    try
                    {
                        dto.Name = string.IsNullOrWhiteSpace((fieldData ?? new()).GetLeadName()) ? "Facebook Enquiry" : (fieldData ?? new()).GetLeadName();
                    }
                    catch { }
                    try
                    {
                        dto.CreatedDate = fbLeadDto?.CreatedTime ?? DateTime.UtcNow;
                    }
                    catch { }
                    try
                    {
                        var input = ExtractFacebookContactNumber(fieldData);
                        if (string.IsNullOrEmpty(input))
                        {
                            input = fieldData?.FirstOrDefault(i => !(i.Name?.ToLower()?.Contains("budget") ?? false) && !(i.Name?.ToLower()?.Contains("cost") ?? false) && !(i.Name?.ToLower()?.Contains("price") ?? false) && i.Values != null && i.Values.Any(j => Regex.Match(j ?? string.Empty, @"[0-9]+$").Value.Length >= 10))?.Values?[0];
                            //input = fieldData?.FirstOrDefault(i => i.Values?.Any(i => (Regex.Replace(i, @"[\(\) -]", "")?.Length > 10) && (Regex.Replace(i, @"[\(\) -]", "")?.Substring(input.Length - 10)?.All(char.IsDigit) ?? false)) ?? false).values?.FirstOrDefault();
                        }
                        if (!string.IsNullOrWhiteSpace(input))
                        {
                            //removing (,),-, , from the string
                            input = Regex.Replace(input, @"[\(\) -]", "");
                            if (input.Contains("+91") && input.Length >= 10)
                            {
                                Match match = Regex.Match(input ?? string.Empty, @"[0-9]+$");
                                dto.ContactNo = "+91" + match.Value?[^10..] ?? string.Empty;
                            }
                            else if (input.All(c => c == '+' || char.IsDigit(c)))
                            {
                                dto.ContactNo = input;
                            }
                            else
                            {
                                dto.ContactNo ??= fieldData?.FirstOrDefault(i => i.Name == _phone || i.Name == _leadPhone || (i.Name?.ToLower()?.Contains("phone") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                                dto.Tag ??= "Invalid ContactNo";
                            }
                        }
                        else
                        {
                            dto.ContactNo ??= fieldData?.FirstOrDefault(i => i.Name == _phone || i.Name == _leadPhone || (i.Name?.ToLower()?.Contains("phone") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                            dto.Tag ??= "Invalid ContactNo";
                        }
                    }
                    catch (Exception e)
                    {
                        dto.ContactNo = fieldData?.FirstOrDefault(i => i.Name == _phone || i.Name == _leadPhone || (i.Name?.ToLower()?.Contains("phone") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
                        dto.Tag = $"Invalid ContactNo - Error Message: {e?.InnerException?.Message ?? e?.Message}";
                    }
                }
                result.Add(dto);
            }
            return result;
        }
        private static string GetLeadName(this List<LeadFormData> fieldData)
        {
            const string _name = "name";
            const string _fullname = "fullname";

            List<string> nameCombinations = new()
            {
                NameConstants.FullName,
                NameConstants.Full_Name,
                NameConstants.FullNameWithSpace,
                NameConstants.YourName,
                NameConstants.Your_Name,
                NameConstants.YourNameWithSpace,
                NameConstants.BuyerName,
                NameConstants.BuyerNameUnderscore,
                NameConstants.Buyer,
                NameConstants.BuyerNameWithSpace
            };
            List<string> nameCombinaitonsToSkip = new()
            {
                NameConstants.ProjectName,
                NameConstants.ProjectNameWithSpace,
                NameConstants.PropertyName,
                NameConstants.Property_Name,
                NameConstants.PropertyNameWithSpace,
                NameConstants.SiteName,
                NameConstants.LocationName,
                NameConstants.LocationNameWithSpace,
                NameConstants.Location_Name
            };

            string name = string.Empty;

            name = fieldData.FirstOrDefault(i => nameCombinations.Any(j => (i.Name ?? string.Empty).ToLower().Contains(j)) && nameCombinaitonsToSkip.All(j => !(i.Name ?? string.Empty).ToLower().Contains(j)))?.Values?[0] ?? string.Empty;
            if (string.IsNullOrWhiteSpace(name))
            {
                if (string.IsNullOrWhiteSpace(name))
                {
                    name = fieldData.FirstOrDefault(i => (i.Name ?? string.Empty).ToLower().Equals(_fullname))?.Values?.FirstOrDefault() ?? string.Empty;

                }
                if (string.IsNullOrWhiteSpace(name))
                {
                    name = fieldData.FirstOrDefault(i => (i.Name ?? string.Empty).ToLower().Equals(_name))?.Values?.FirstOrDefault() ?? string.Empty;
                }
                if (string.IsNullOrWhiteSpace(name))
                {

                    string firstName = fieldData.FirstOrDefault(i => (i.Name ?? string.Empty).ToLower().Contains(_name))?.Values?.FirstOrDefault() ?? string.Empty;
                    string lastName = fieldData.LastOrDefault(i => (i.Name ?? string.Empty).ToLower().Contains(_name))?.Values?.FirstOrDefault() ?? string.Empty;
                    name = $"{firstName} {lastName}".Trim();
                }
            }
            return name;
        }
        public static class NameConstants
        {
            //to take
            public const string FullName = "fullname";
            public const string Full_Name = "full_name";
            public const string FullNameWithSpace = "full name";
            public const string YourName = "yourname";
            public const string Your_Name = "your_name";
            public const string YourNameWithSpace = "your name";
            public const string BuyerName = "buyer's name";
            public const string BuyerNameUnderscore = "buyer_name";
            public const string Buyer = "buyer";
            public const string BuyerNameWithSpace = "buyer name";

            //to skip
            public const string ProjectName = "project_name";
            public const string ProjectNameWithSpace = "project name";
            public const string PropertyName = "propertyname";
            public const string Property_Name = "property_name";
            public const string PropertyNameWithSpace = "property name";
            public const string SiteName = "site name";
            public const string LocationName = "location name";
            public const string LocationNameWithSpace = "locationname";
            public const string Location_Name = "location_name";
        }

        private static string ExtractFacebookContactNumber(List<LeadFormData>? fieldData)
        {
            const string _leadPhone = "lead_phone_number";
            const string _phone = "phone_number";
            string input = string.Empty;
            if (fieldData == null) { return input; }
            input = fieldData.FirstOrDefault(i => i.Name == _phone)?.Values?.FirstOrDefault() ?? string.Empty;
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => i.Name == _leadPhone)?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("phone") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("mob") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("contact") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            if (string.IsNullOrWhiteSpace(input))
            {
                input = fieldData.FirstOrDefault(i => (i?.Name?.ToLower()?.Contains("call") ?? false))?.Values?.FirstOrDefault() ?? string.Empty;
            }
            return input;
        }
    }

}



