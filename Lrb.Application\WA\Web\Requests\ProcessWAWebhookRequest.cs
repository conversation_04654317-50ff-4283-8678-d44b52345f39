﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web;
using Lrb.Application.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace Lrb.Application.WA.Web
{
    public class ProcessWAWebhookRequest : IRequest<Response<bool>>
    {
        public HttpRequest HttpRequest { get; set; }
        public string Tenant { get; set; }
        public Guid AccountId { get; set; }
    }
    public class ProcessWAWebhookRequestHandler : IRequestHandler<ProcessWAWebhookRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.WAMessage> _waMessageRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;

        public ProcessWAWebhookRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepo,
            IRepositoryWithEvents<Domain.Entities.WAMessage> waMessageRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo)
        {
            _integrationAccountInfoRepo = integrationAccountInfoRepo;
            _waMessageRepo = waMessageRepo;
            _leadRepo = leadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _globalSettingsRepo = globalSettingsRepo;
        }
        public async Task<Response<bool>> Handle(ProcessWAWebhookRequest request, CancellationToken cancellationToken)
        {
            var integrationAccountInfo = await _integrationAccountInfoRepo.FirstOrDefaultAsync(new GetIntegrationAccountInfoWithWASpec(request.AccountId), cancellationToken);
            var waWebhookMapping = integrationAccountInfo?.WAPayloadMapping?.WebhookMapping;
            var statusMapping = integrationAccountInfo?.WAPayloadMapping?.StatusMapping;
            if (waWebhookMapping == null || statusMapping == null)
            {
                return new(false)
                {
                    Succeeded = false,
                    Message = "No Payload Info was found for the given account"
                };
            }
            var mappings = await GetMappedValues(request.HttpRequest, waWebhookMapping, statusMapping);
            Dictionary<string, string> mappedValues = mappings.Item1;
            Dictionary<WAEvent, string> mappedEvents = mappings.Item2;
            WAWebhookDto waWebhookDto = GetWebhookDto(mappedValues);
            var customerNo = waWebhookDto.CustomerNo ?? string.Empty;
            if (string.IsNullOrWhiteSpace(waWebhookDto.MessageId) || string.IsNullOrWhiteSpace(customerNo))
            {
                return new(false)
                {
                    Message = "MessageId can't be null or empty",
                    Succeeded = true,
                    Data = false,
                };
            }
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);
            var defaultCountryCode = countries?.FirstOrDefault()?.DefaultCallingCode;
            List<string> contactNos = new List<string>() {  customerNo, 
                                                            customerNo.Length >= 10 ? customerNo[^10..] : customerNo, 
                                                            customerNo.Length > 10 ? customerNo.Substring(1) : customerNo,
                                                            customerNo.Length == 10 && !string.IsNullOrWhiteSpace(defaultCountryCode) ? defaultCountryCode + customerNo : customerNo,
                                                            customerNo.Length == 10 && !string.IsNullOrWhiteSpace(defaultCountryCode) ? defaultCountryCode.Substring(1) + customerNo : customerNo};
            contactNos = contactNos.Distinct().ToList();
            var existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(contactNos), cancellationToken);
            var waMessages = await _waMessageRepo.ListAsync(new GetWAMessageByCustomSpec(waWebhookDto.MessageId), cancellationToken);

            if (existingLeads.Any())
            {
                if (!waMessages.Any())
                {
                    var newMessage = waWebhookDto.Adapt<Domain.Entities.WAMessage>();
                    newMessage.CustomerId = existingLeads.Select(i => i.Id).FirstOrDefault();
                    var waMessage = await _waMessageRepo.AddAsync(newMessage);
                    waMessages.Add(waMessage);
                }
                WAEvent waEvent = mappedEvents.FirstOrDefault().Key;
                waMessages.ForEach(i => i.WAEvent = waEvent);
                await _waMessageRepo.UpdateRangeAsync(waMessages);

                var leadIds = existingLeads.Select(i => i.Id).ToList();
                var waMessageLeadIds = waMessages.Select(i => i.CustomerId).ToList();
                var leadIdsToAdd = leadIds.Where(i => !waMessageLeadIds.Contains(i)).ToList();
                List<Domain.Entities.WAMessage> waMessagesToCreate = new();
                foreach (var leadId in leadIdsToAdd)
                {
                    var newWAMessage = waMessages.FirstOrDefault()?.Adapt<Domain.Entities.WAMessage>();
                    if (newWAMessage != null)
                    {
                        newWAMessage.CustomerId = leadId;
                        newWAMessage.Id = Guid.NewGuid();
                        waMessagesToCreate.Add(newWAMessage);
                    }
                }
                if (waMessagesToCreate.Any())
                {
                    await _waMessageRepo.AddRangeAsync(waMessagesToCreate);
                }
            }
            else
            {
                WhatsAppListingSitesIntegrationRequest lead = new()
                {
                    ApiKey = integrationAccountInfo.ApiKey,
                    Mobile = waWebhookDto.CustomerNo,
                    LeadSource = LeadSource.WhatsApp,
                    Name = "WA" + TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("India Standard Time")).ToString("dd-MM-yyyy HH:mm:ss")
                };

                RestClient client = new RestClient("https://localhost:44355/api/v1/integration");
                RestRequest? restRequest = new RestRequest("/whatsapp", Method.Post);
                restRequest.AddHeader("accept", "application/json");
                restRequest.AddHeader("API-Key", integrationAccountInfo.ApiKey ?? string.Empty);
                restRequest.AddHeader("Content-Type", "application/json");
                restRequest.AddBody(lead);
                //existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(contactNos), cancellationToken);
                //if (existingLeads.Any())
                //{
                //    return new(false)
                //    {
                //        Succeeded = false,
                //        Message = "Lead Not Created"
                //    };
                //}
                var res = await client.ExecuteAsync(restRequest);
                if (res.IsSuccessStatusCode)
                {
                    existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(contactNos), cancellationToken);
                    if (!waMessages.Any())
                    {
                        var newMessage = waWebhookDto.Adapt<Domain.Entities.WAMessage>();
                        newMessage.CustomerId = existingLeads.OrderByDescending(i => i.CreatedOn).Select(i => i.Id).FirstOrDefault();
                        var waMessage = await _waMessageRepo.AddAsync(newMessage);
                        waMessages.Add(waMessage);
                    }
                    WAEvent waEvent = mappedEvents.FirstOrDefault().Key;
                    waMessages.ForEach(i => i.WAEvent = waEvent);
                    await _waMessageRepo.UpdateRangeAsync(waMessages);

                    var leadIds = existingLeads.Select(i => i.Id).ToList();
                    var waMessageLeadIds = waMessages.Select(i => i.CustomerId).ToList();
                    var leadIdsToAdd = leadIds.Where(i => !waMessageLeadIds.Contains(i)).ToList();
                    List<Domain.Entities.WAMessage> waMessagesToCreate = new();
                    foreach (var leadId in leadIdsToAdd)
                    {
                        var newWAMessage = waMessages.FirstOrDefault()?.Adapt<Domain.Entities.WAMessage>();
                        if (newWAMessage != null)
                        {
                            newWAMessage.CustomerId = leadId;
                            newWAMessage.Id = Guid.NewGuid();
                            waMessagesToCreate.Add(newWAMessage);
                        }
                    }
                    if (waMessagesToCreate.Any())
                    {
                        await _waMessageRepo.AddRangeAsync(waMessagesToCreate);
                    }
                }
                else
                {
                    return new(false)
                    {
                        Succeeded = false,
                        Message = "Lead Not Created"
                    };
                }
            }
            return new(true)
            {
                Succeeded = true,
                Message = "Request Processed Successfully"
            };
        }
        public WAWebhookDto GetWebhookDto(Dictionary<string, string> mappedValues)
        {
            WAWebhookDto waWebhookDto = new()
            {
                CustomerNo = mappedValues.GetValueOrDefault("#CustomerNo#"),
                DeliveryStatus = mappedValues.GetValueOrDefault("#DeliveryStatus#"),
                MediaUrl = mappedValues.GetValueOrDefault("#MediaUrl#"),
                Message = mappedValues.GetValueOrDefault("#Message#"),
                MessageId = mappedValues.GetValueOrDefault("#MessageId#")
            };
            return waWebhookDto;
        }
        public async Task<(Dictionary<string, string>, Dictionary<WAEvent, string>)> GetMappedValues(HttpRequest httpRequest, IDictionary<string, string> waWebhookMapping, IDictionary<WAEvent, string> waStatusMapping)
        {
            Stream stream = httpRequest.Body;
            HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
            var bodyInString = await reader.ReadToEndAsync();
            var parsedObj = JObject.Parse(bodyInString);
            var keyValues = JTokenHelper.GetKeysAndValues(parsedObj);
            Dictionary<string, string> mappedBody = GetUnMappedParameters(keyValues, waWebhookMapping);
            Dictionary<WAEvent, string> statusBody = GetUnMappedStatusParameters(keyValues, waStatusMapping);

            return (mappedBody, statusBody);
        }
        public Dictionary<string, string> GetUnMappedParameters(IDictionary<string, string> requestPayload, IDictionary<string, string> waWebhookMapping)
        {
            Dictionary<string, string> mappedBody = new();
            string? val = string.Empty;
            foreach (var map in waWebhookMapping)
            {
                var value = requestPayload.TryGetValue(map.Value, out val) ? val?.ToString() : string.Empty;
                mappedBody.Add(map.Key, value ?? string.Empty);
            }
            mappedBody = mappedBody.Where(i => !string.IsNullOrWhiteSpace(i.Value)).ToDictionary(i => i.Key, j => j.Value);
            return mappedBody;
        }
        public Dictionary<WAEvent, string> GetUnMappedStatusParameters(IDictionary<string, string> requestPayload, IDictionary<WAEvent, string> waWebhookMapping)
        {
            Dictionary<WAEvent, string> mappedBody = new();
            string? val = string.Empty;
            foreach (var map in waWebhookMapping)
            {
                var value = requestPayload.TryGetValue(map.Value, out val) ? val?.ToString() : string.Empty;
                if (!string.IsNullOrWhiteSpace(value))
                {
                    mappedBody.Add(map.Key, value ?? string.Empty);
                    break;
                }
            }
            mappedBody = mappedBody.Where(i => !string.IsNullOrWhiteSpace(i.Value)).ToDictionary(i => i.Key, j => j.Value);
            return mappedBody;
        }
    }
}
