﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Source.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using PhoneNumbers;
using System;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using ThirdParty.Json.LitJson;

namespace Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation
{
    // need to remove this function.
    public static class BulkUploadHelper
    {
        public static List<Domain.Entities.Lead> ConvertToLeads(
            this DataTable table,
            Dictionary<DataColumns, string>? mappedColumnsData,
            List<string> unMappedColumns,
            List<Lrb.Domain.Entities.Project> projects,
            List<Domain.Entities.Property> properties,
            List<MasterPropertyType> propertyTypes,
            List<MasterAreaUnit> areaUnits,
            List<MasterLeadStatus> leadStatuses,
            List<UserDetailsDto> users)
        {
            //var newStatus = leadStatuses.FirstOrDefault(i => i.Status == "new");
            List<Domain.Entities.Lead> leads = new();
            Parallel.ForEach<DataRow>(table.Rows.Cast<DataRow>(), row =>
            {
                //  var bHKType = !mappedColumnsData.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) ? default : Enum.TryParse<BHKType>(row[mappedColumnsData[DataColumns.BHKType]].ToString(), true, out var bHKTyp) ? bHKTyp : default;
                var bHKTypes = !mappedColumnsData.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) ? default : row[mappedColumnsData[DataColumns.BHKType]].ToString();
                // var noOfBHK = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? default : CreateLeadHelper.GetNoOfBHK(row[mappedColumnsData[DataColumns.NoOfBHK]].ToString());
                string enquiryTypes = !mappedColumnsData.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.EnquiredFor]) ? string.Empty : row[mappedColumnsData[DataColumns.EnquiredFor]].ToString();
                var noOfBHKs = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? default : row[mappedColumnsData[DataColumns.NoOfBHK]].ToString();
                var beds = !mappedColumnsData.ContainsKey(DataColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Beds]) ? default : row[mappedColumnsData[DataColumns.Beds]].ToString();
                var baths = !mappedColumnsData.ContainsKey(DataColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Baths]) ? default : row[mappedColumnsData[DataColumns.Baths]].ToString();
                var furnished = !mappedColumnsData.ContainsKey(DataColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.FurnishStatus]) ? default : row[mappedColumnsData[DataColumns.FurnishStatus]].ToString();

                var enquiryTypesInfo = GetEnquiryTypesInfo(enquiryTypes ?? string.Empty);
                //var propertyType = (!mappedColumnsData.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BasePropertyType]) || !mappedColumnsData.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubPropertyType]) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType])  || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK])) ? default
                var propertyType = (!mappedColumnsData.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BasePropertyType]) || !mappedColumnsData.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubPropertyType]) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK])) ? default
                : GetPropertyType(row[mappedColumnsData[DataColumns.BasePropertyType]].ToString(), row[mappedColumnsData[DataColumns.SubPropertyType]].ToString(), propertyTypes, bHKTypes, noOfBHKs, beds, baths, furnished);
                leads.Add(new Domain.Entities.Lead()
                {
                    Name = !mappedColumnsData.ContainsKey(DataColumns.Name) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Name]) ? string.Empty : row[mappedColumnsData[DataColumns.Name]].ToString(),
                    ContactNo = !mappedColumnsData.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ContactNo]) ? string.Empty : row[mappedColumnsData[DataColumns.ContactNo]].ToString(),
                    Email = !mappedColumnsData.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Email]) ? string.Empty : row[mappedColumnsData[DataColumns.Email]].ToString(),
                    Rating = !mappedColumnsData.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Rating]) ? string.Empty : row[mappedColumnsData[DataColumns.Rating]].ToString(),
                    //Status = newStatus,
                    Enquiries = new List<LeadEnquiry>() {
                    new(){
                        IsPrimary = true,
                        EnquiredFor = !mappedColumnsData.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.EnquiredFor]) ? default : Enum.TryParse<EnquiryType>(row[mappedColumnsData[DataColumns.EnquiredFor]].ToString(), true, out var enquiryType) ? enquiryType : default,
                        EnquiryTypes=enquiryTypesInfo.IsValidInfo ? enquiryTypesInfo.EnquiryTypes : default,
                        LeadSource = !mappedColumnsData.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Source]) ? default : Enum.TryParse<Domain.Enums.LeadSource>(row[mappedColumnsData[DataColumns.Source]].ToString(), true, out var leadsource) ? leadsource : default,
                        NoOfBHKs = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? default : CreateLeadHelper.GetNoOfBHK(row[mappedColumnsData[DataColumns.NoOfBHK]].ToString()),
                        BHKs=propertyType.IsValidInfo ? propertyType.BHKs : default,
                        Addresses = new()
                        {
                            new()
                            {
                                City = !mappedColumnsData.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.City]) ? string.Empty : row[mappedColumnsData[DataColumns.City]].ToString(),
                                State = !mappedColumnsData.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.State]) ? string.Empty : row[mappedColumnsData[DataColumns.State]].ToString(),
                                SubLocality = !mappedColumnsData.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Location]) ? string.Empty : row[mappedColumnsData[DataColumns.Location]].ToString(),
                            }
                        }
                    } },
                    Projects = !mappedColumnsData.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Project]) || string.IsNullOrWhiteSpace(row[mappedColumnsData[DataColumns.Project]]?.ToString()) ? null : new List<Lrb.Domain.Entities.Project>() { projects.FirstOrDefault(i => i.Name == (!mappedColumnsData.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Project]) ? default : row[mappedColumnsData[DataColumns.Project]].ToString())) },
                    Properties = !mappedColumnsData.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Property]) || string.IsNullOrWhiteSpace(row[mappedColumnsData[DataColumns.Property]].ToString()) ? new List<Domain.Entities.Property>() : new List<Domain.Entities.Property>() { properties.FirstOrDefault(i => i.Title == (!mappedColumnsData.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Property]) ? default : row[mappedColumnsData[DataColumns.Property]].ToString())) },
                    AlternateContactNo = !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternateContactNo]) ? string.Empty : row[mappedColumnsData[DataColumns.AlternateContactNo]].ToString(),
                    Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,

                });
            });
            return leads;

        }

        private static DateTime? GetDataTimeValue(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row, string jsonData = null)
        {
            try
            {


                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    if (column == DataColumns.CreatedDate)
                    {
                        return DateTime.UtcNow;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    DateTime? date = null;
                    try
                    {
                        if (double.TryParse(dateString, out var value))
                        {
                            date = DateTime.FromOADate(value);
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(jsonData))
                            {
                                CommonTimeZoneDto commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                                date = DateTime.Parse(dateString).ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);

                            }
                            else
                            {
                                return Convert.ToDateTime(dateString, CultureInfo.GetCultureInfo("hi-IN")).ToUniversalTime();

                            }

                        }
                        /*if (date.HasValue)
                        {

                            date = date.Value.ToUniversalTime();
                            return date.Value.ToUniversalTime();
                        }*/
                    }
                    catch
                    {
                        return date;
                    }
                    return date;
                }
            }
            catch
            {
                return DateTime.UtcNow;
            }

        }

        private static CustomMasterLeadStatus? GetLeadStatus(this Dictionary<DataColumns, string> mappedDataColumns,
           DataRow row, List<CustomMasterLeadStatus> leadStatuses)
        {
            CustomMasterLeadStatus? leadStatus = null;
            var baseStatusString = mappedDataColumns.GetStringValue(DataColumns.BaseStatus, row);
            var subStatusString = mappedDataColumns.GetStringValue(DataColumns.SubStatus, row);
            if (!string.IsNullOrWhiteSpace(subStatusString))
            {
                if (!string.IsNullOrWhiteSpace(baseStatusString))
                {
                    var subStatuses = leadStatuses.Where(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == subStatusString.ToLower().Replace(" ", "")) || i.Status == subStatusString.Replace(" ", "_").ToLower())).ToList();
                    var baseStatus = leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == baseStatusString.ToLower().Replace(" ", "")) || i.Status == baseStatusString.Replace(" ", "_").ToLower()));
                    if (baseStatus != null)
                    {
                        leadStatus = subStatuses.FirstOrDefault(i => i.BaseId == baseStatus.Id);
                    }
                }
                leadStatus ??= leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == subStatusString.ToLower().Replace(" ", "")) || i.Status == subStatusString.Replace(" ", "_").ToLower()));
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(baseStatusString))
                {
                    leadStatus = leadStatuses.FirstOrDefault(i => !i.IsDeleted && ((i.DisplayName != null && i.DisplayName.ToLower().Replace(" ", "") == baseStatusString.ToLower().Replace(" ", "")) || i.Status == baseStatusString.Replace(" ", "_").ToLower()));
                }
            }
            return leadStatus != null && leadStatus.IsValidStatus(leadStatuses) ? leadStatus : null;
        }
        private static bool IsValidStatus(this CustomMasterLeadStatus leadStatus, List<CustomMasterLeadStatus> leadStatuses)
        {
            if (leadStatus == null)
            {
                return false;
            }
            else
            {
                if (leadStatus.BaseId == default || leadStatus.BaseId == null)
                {
                    return !leadStatuses.Any(i => i.BaseId == leadStatus.Id);
                }
                else
                {
                    return leadStatuses.Any(i => i.Id == leadStatus.BaseId);
                }
            }
        }
        private static string GetStringValue(this Dictionary<DataColumns, string> mappedColumnsData, DataColumns column, DataRow row)
        {
            var data = !mappedColumnsData.ContainsKey(column)
                    || string.IsNullOrEmpty(mappedColumnsData[column])
                    ? string.Empty
                    : row[mappedColumnsData[column]]?.ToString();
            return data?.Trim() ?? string.Empty;
        }

        public static PropertyTypeInfo GetPropertyType(string basePropertyType, string subPropertyType, List<MasterPropertyType> propertyTypes, string bhkType, string? noOfBHK, string? beds = null, string? baths = null, string? furnishedstatus = null, string? floors = null, LeadSource? leadSource = null)

        {
            List<MasterPropertyType> propertyType = new();

            List<string> proprtyTypes = propertyTypes?.Select(i => i.DisplayName.ToLower()).ToList();
            //List<string> proprtyTypes = new() { "flat", "independent house", "villa", "residential" ,"commercial","agricultural","plot","shop","agricultural land"};

            List<BHKType> bhkTypes = new();
            List<double> listOfBHKs = new();
            List<int> listOfBeds = new();
            List<int> listOfBaths = new();
            FurnishStatus FurnishStatus = new();
            List<string> listOfFloors = new();
            List<string> subPropertyTypes = new();


            //List<string> proprtyTypes = new() { "flat", "independent house", "villa", "residential" ,"commercial","agricultural","plot","shop","agricultural land"};
            if (!string.IsNullOrEmpty(subPropertyType))
            {
                foreach (string subProp in subPropertyType.Split(','))
                {
                    if (!string.IsNullOrWhiteSpace(subProp))
                    {
                        subPropertyTypes.Add(subProp.Trim());
                    }
                }


            }
            if (!string.IsNullOrEmpty(bhkType))
            {
                foreach (string bhk in bhkType.Split(','))
                {
                    if (Enum.TryParse<BHKType>(bhk, true, out BHKType type))
                    {
                        bhkTypes.Add(type);
                    }
                }
            }
            if (!string.IsNullOrEmpty(noOfBHK))
            {
                foreach (string bhk in noOfBHK.Split(','))
                {
                    double bHK = CreateLeadHelper.GetNoOfBHK(bhk);
                    if (bHK != 0)
                    {
                        listOfBHKs.Add(bHK);
                    }
                }
            }
            if (!string.IsNullOrEmpty(floors))
            {
                foreach (string floor in floors.Split(','))
                {
                    if (!string.IsNullOrWhiteSpace(floor))
                    {
                        listOfFloors.Add(floor);
                    }
                }


            }
            if (!string.IsNullOrWhiteSpace(furnishedstatus))
            {
                foreach (string furnished in furnishedstatus.Split(','))
                {
                    if (Enum.TryParse<FurnishStatus>(furnished, true, out FurnishStatus type))
                    {
                        FurnishStatus = type;
                    }
                }
            }
            if (!string.IsNullOrWhiteSpace(beds))
            {
                foreach (string bed in beds.Split(','))
                {
                    int noBed = CreateLeadHelper.Get(bed);
                    listOfBeds.Add(noBed);
                }
            }
            if (!string.IsNullOrWhiteSpace(baths))
            {
                foreach (string bath in baths.Split(','))
                {
                    int noBaths = CreateLeadHelper.Get(bath);
                    if (noBaths != 0)
                    {
                        listOfBaths.Add(noBaths);
                    }
                }
            }
            //if (bhkTypes.Count > 0 || listOfBHKs.Count > 0)
            //{
            //    if (!proprtyTypes.Contains(subPropertyType.ToLower()))
            //    {
            //        return new PropertyTypeInfo()
            //        {
            //            InvalidBHKType = bhkType,
            //            InvalidNoOfBHK = noOfBHK,
            //            IsValidInfo = false,
            //            BasePropertyType = basePropertyType.ToLower(),
            //            SubPropertyType = subPropertyType
            //        };
            //    }
            //}
            if (string.IsNullOrEmpty(basePropertyType) && string.IsNullOrEmpty(subPropertyType))
            {
                return new PropertyTypeInfo() { IsValidInfo = false };
            }

            string subProperty = string.Empty;
            string baseProperty = string.Empty;
            List<string> subProperties = new();

            if (subPropertyTypes != null && subPropertyTypes.Any())

            {
                foreach (var subPrope in subPropertyTypes)
                {
                    if (!string.IsNullOrEmpty(subPrope))
                    {
                        if (leadSource != null && leadSource == LeadSource.PropertyFinder)
                        {
                            subProperties.Add(subProperty);
                        }
                        else
                        {
                            if (subPrope.ToLower() == "apartment" || subPrope.ToLower() == "studio apartment" || subPrope.ToLower() == "1rk" || subPrope.ToLower() == "serviced apartment" || subPrope.ToLower() == "penthouse")
                            {
                                subProperty = "flat";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "independent" || subPrope.ToLower() == "builder floor" || subPrope.ToLower() == "farmhouse")
                            {
                                subPropertyType = "independent house";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "office" || subPrope.ToLower() == "office in it park")
                            {
                                subPropertyType = "office space";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "retail")
                            {
                                subPropertyType = "shop";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "storage")
                            {
                                subPropertyType = "basement";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "industry")
                            {
                                subPropertyType = "industrial space";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "hospitality")
                            {
                                subPropertyType = "hotel space";
                                subProperties.Add(subProperty);

                            }
                            else if (subPrope.ToLower() == "warehouse")
                            {
                                subPropertyType = "godown";
                                subProperties.Add(subProperty);
                            }

                        }
                    }
                }

            }
            else
            {
                if (!string.IsNullOrEmpty(basePropertyType))
                {
                    if (basePropertyType.ToLower().Contains("residential") || basePropertyType.ToLower() == "r")
                    {
                        subProperty = "flat";
                        subProperties.Add(subProperty);
                    }
                    if (basePropertyType.ToLower().Contains("commercial") || basePropertyType.ToLower() == "c")
                    {
                        subProperty = "Plot";
                        subProperties.Add(subProperty);
                    }
                    if (basePropertyType.ToLower().Contains("agricultural") || basePropertyType.ToLower() == "a")
                    {
                        subProperty = "land";
                        subProperties.Add(subProperty);
                    }
                }
            }

            if (!string.IsNullOrEmpty(basePropertyType) && !basePropertyType.ToLower().Contains("agricultural") && basePropertyType.ToLower() != "a" && subProperty.ToLower() == "land")
            {
                subProperty = "plot";
            }

            if (string.IsNullOrEmpty(basePropertyType) && subPropertyTypes.Any())
            {
                var sub = subPropertyTypes?.FirstOrDefault();
                if (propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted) != null)
                {
                    basePropertyType = "residential";
                }
                else if (propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted) != null)
                {
                    basePropertyType = "commercial";
                }
                else if (propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted) != null)
                {
                    basePropertyType = "agricultural";
                }
            }


            if (!string.IsNullOrEmpty(basePropertyType) && (basePropertyType?.ToLower() == "commercial" || basePropertyType?.ToLower() == "c"))
            {
                foreach (var sub in subPropertyTypes)
                {
                    var propertyTypeadd = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted);

                    if (propertyTypeadd != null)
                    {
                        propertyType.Add(propertyTypeadd);
                    }
                }
            }
            else if (string.IsNullOrEmpty(basePropertyType) || (basePropertyType?.ToLower() == "residential" || basePropertyType?.ToLower() == "r"))
            {
                foreach (var sub in subPropertyTypes)
                {
                    var propertyTypeadd = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted);
                    if (propertyTypeadd != null)
                    {
                        propertyType.Add(propertyTypeadd);
                    }
                }
            }
            else if (!string.IsNullOrEmpty(basePropertyType) && (basePropertyType?.ToLower() == "agricultural" || basePropertyType?.ToLower() == "a"))
            {
                foreach (var sub in subPropertyTypes)
                {
                    var propertyTypeadd = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") && i.DisplayName.ToLower() == sub.ToLower() && !i.IsDeleted);
                    if (propertyTypeadd != null)

                    {
                        propertyType.Add(propertyTypeadd);
                    }
                }
            }
            return new PropertyTypeInfo()
            {
                PropertyTypes = propertyType,
                PropertyType = propertyType?.FirstOrDefault(),
                BHKTypes = bhkTypes,
                BHKs = listOfBHKs,
                Beds = listOfBeds,
                Baths = listOfBaths,
                Furnished = FurnishStatus,
                Floors = listOfFloors,
                IsValidInfo = true
            };
        }
        public static List<string> GetUnmappedColumnNames(this DataTable table, Dictionary<DataColumns, string> mappedColumnsData)
        {
            List<string> columns = new();
            if (mappedColumnsData.ContainsKey(DataColumns.Notes))
            {
                if (!string.IsNullOrEmpty(mappedColumnsData[DataColumns.Notes]))
                {
                    columns.Add(mappedColumnsData[DataColumns.Notes]);
                    mappedColumnsData.Remove(DataColumns.Notes);
                }

            }
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }
        public static void SetLead(this Domain.Entities.Lead lead,
            Dictionary<DataColumns, string>? mappedColumnsData, Guid currentUserId)
        {
            try
            {
                if (lead != null)
                {
                    /* if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                     {
                         var multiNumbers = lead.ContactNo.Contains(',') ? lead.ContactNo.Split(',') : lead.ContactNo.Contains('\\') ? lead.ContactNo.Split('\\') : lead.ContactNo.Split('/');
                        *//* if (multiNumbers.Length > 1 && !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo))
                         {
                             lead.AlternateContactNo = multiNumbers[1];
                         }*/
                    /* lead.ContactNo = multiNumbers[0];
                     if (lead.ContactNo.ToLower().Contains('e'))
                     {
                         if (double.TryParse(lead.ContactNo.Replace("+91", ""), out double cNumber))
                         {
                             lead.ContactNo = (cNumber).ToString().Split('.')[0];
                         }
                     }*/
                    /*if(lead.ContactNo.Length==12 && lead.ContactNo.StartsWith("91"))
                    {
                        lead.ContactNo = $"+{lead.ContactNo.Trim()}";
                    }
                    if (lead.ContactNo.Length==10)
                    {
                        lead.ContactNo = $"+91{lead.ContactNo.Trim()}";
                    }
                    else
                    {
                        lead.ContactNo = $"+{lead.ContactNo.Trim()}";
                    }*//*

                   // lead.ContactNo = Regex.Replace(lead.ContactNo, @"[^0-9]+", "");
                    //if (lead.ContactNo.Length > 10) { lead.ContactNo = lead.ContactNo[^10..]; }; 
                    //lead.ContactNo = $"+91{lead.ContactNo.Trim()}";
                }*/
                    string name = lead.Name?.Trim() ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(name))
                    {
                        lead.LeadNumber ??= (char)(new Random().Next(65, 90)) + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                    }
                    lead.TagInfo ??= new();
                    lead.CreatedBy = currentUserId;
                    lead.LastModifiedBy = currentUserId;
                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public static void AssignLead(this List<Domain.Entities.Lead> leads, List<Guid> userIds, Guid currentUserId)
        {
            #region Assigning User
            try
            {
                if (userIds != null && userIds.Count > 0)
                {
                    var filteredUserIds = userIds.Where(i => i != null).ToList();

                    if (filteredUserIds.Count > 0 && leads.Any())
                    {
                        int userCount = filteredUserIds.Count;
                        int index = 0;
                        foreach (var lead in leads)
                        {
                            if (index < userCount)
                            {
                                lead.AssignTo = filteredUserIds[index];
                                lead.AssignedFrom = currentUserId;
                                lead.OriginalOwner = lead.AssignTo;
                            }
                            else
                            {
                                index = 0;
                                lead.AssignTo = filteredUserIds[index];
                                lead.AssignedFrom = currentUserId;
                                lead.OriginalOwner = lead.AssignTo;
                            }
                            index++;
                        }
                    }
                }
            }
            catch (Exception e)
            {

            }
            #endregion
        }
        public static void SetUsersInViewLeadDtos(this List<ViewLeadDto> leadDtos, List<UserDetailsDto> users)
        {
            leadDtos.ForEach(leadDto =>
            {
                var assignedUser = users.FirstOrDefault(i => i.Id == leadDto.AssignTo);
                leadDto.AssignedUser = new()
                {
                    Id = assignedUser?.Id ?? Guid.Empty,
                    ContactNo = assignedUser?.PhoneNumber ?? "",
                    Name = (assignedUser?.FirstName ?? "") + (assignedUser?.LastName ?? "")
                };
                var assignedFromUser = users.FirstOrDefault(i => i.Id == leadDto.AssignedFrom);
                leadDto.AssignedFromUser = new()
                {
                    Id = assignedFromUser?.Id ?? Guid.Empty,
                    ContactNo = assignedFromUser?.PhoneNumber ?? "",
                    Name = (assignedFromUser?.FirstName ?? "") + (assignedFromUser?.LastName ?? "")
                };
                var lastModifiedUser = users.FirstOrDefault(i => i.Id == leadDto.LastModifiedBy);
                leadDto.LastModifiedByUser = new()
                {
                    Id = lastModifiedUser?.Id ?? Guid.Empty,
                    ContactNo = lastModifiedUser?.PhoneNumber ?? "",
                    Name = (lastModifiedUser?.FirstName ?? "") + (lastModifiedUser?.LastName ?? "")
                };

            });
        }
        public static void SetUsersInViewLeadDto(this ViewLeadDto leadDto, List<UserDetailsDto> users, Guid? currentUserId = null)
        {
            var assignedUser = users.FirstOrDefault(i => i.Id == leadDto.AssignTo);
            leadDto.AssignedUser = new()
            {
                Id = assignedUser?.Id ?? Guid.Empty,
                ContactNo = assignedUser?.PhoneNumber ?? "",
                Name = (assignedUser?.FirstName + " " ?? "") + (assignedUser?.LastName ?? "")
            };
            var assignedFromUser = users.FirstOrDefault(i => i.Id == leadDto.AssignedFrom);
            leadDto.AssignedFromUser = new()
            {
                Id = assignedFromUser?.Id ?? Guid.Empty,
                ContactNo = assignedFromUser?.PhoneNumber ?? "",
                Name = (assignedFromUser?.FirstName + " " ?? "") + (assignedFromUser?.LastName ?? "")
            };
            var lastModifiedUser = users.FirstOrDefault(i => i.Id == (currentUserId ?? leadDto.LastModifiedBy));
            leadDto.LastModifiedByUser = new()
            {
                Id = lastModifiedUser?.Id ?? Guid.Empty,
                ContactNo = lastModifiedUser?.PhoneNumber ?? "",
                Name = (lastModifiedUser?.FirstName + " " ?? "") + (lastModifiedUser?.LastName ?? "")
            };
            if (leadDto.SecondaryUserId != null)
            {
                var secondaryAssignedUser = users.FirstOrDefault(i => i.Id == leadDto.SecondaryUserId);
                leadDto.SecondaryUser = new()
                {
                    Id = secondaryAssignedUser?.Id ?? Guid.Empty,
                    ContactNo = secondaryAssignedUser?.PhoneNumber ?? "",
                    Name = (secondaryAssignedUser?.FirstName + " " ?? "") + (secondaryAssignedUser?.LastName ?? "")
                };
            }
            if(leadDto.SecondaryFromUserId != null)
            {
                var secondaryAssignedFromUser = users.FirstOrDefault(i => i.Id == leadDto.SecondaryFromUserId);
                leadDto.SecondaryFromUser = new()
                {
                    Id = secondaryAssignedFromUser?.Id ?? Guid.Empty,
                    ContactNo = secondaryAssignedFromUser?.PhoneNumber ?? "",
                    Name = (secondaryAssignedFromUser?.FirstName + " " ?? "") + (secondaryAssignedFromUser?.LastName ?? "")
                };
            }
            

        }

        public static EnquiryForInfo GetEnquiryForInfo(string enquiryFor)
        {
            EnquiryForInfo enquiryForInfo = new();
            if (!string.IsNullOrEmpty(enquiryFor) && !string.IsNullOrWhiteSpace(enquiryFor))
            {
                enquiryForInfo.EnquiryTypes = new();
                foreach (string value in enquiryFor.Split(','))
                {
                    if (Enum.TryParse<EnquiryType>(value, true, out var enquiryType))
                    {
                        enquiryForInfo.EnquiryTypes.Add(enquiryType);
                    }
                }
                //enquiryForInfo.EnquiredFor = enquiryType;
                enquiryForInfo.IsValidInfo = true;
                return enquiryForInfo;
            }
            else
            {
                enquiryForInfo.InvalidEnquiredFor = enquiryFor;
                enquiryForInfo.IsValidInfo = false;
                return enquiryForInfo;
            }

        }
        public static EnquiryForInfo GetEnquiryTypesInfo(string enquiryTypes)
        {
            EnquiryForInfo enquiryForInfo = new();
            string[] enquiries = enquiryTypes.Split(',');
            enquiryForInfo.EnquiryTypes = new List<EnquiryType>();
            foreach (string enquiry in enquiries)
            {
                if (Enum.TryParse<EnquiryType>(enquiry.Trim(), true, out var enquiryType))
                {
                    enquiryForInfo.EnquiryTypes.Add(enquiryType);
                    enquiryForInfo.IsValidInfo = true;
                }
                else
                {
                    enquiryForInfo.InvalidEnquiredFor = enquiry.Trim();
                    enquiryForInfo.IsValidInfo = false;
                }
            }
            return enquiryForInfo;
        }
        public static bool TryParseGetEnum<TEnum>(string searchEnum, out TEnum parsedValue) where TEnum : struct, Enum
        {
            return Enum.TryParse<TEnum>(searchEnum, true, out parsedValue)
                ? Enum.IsDefined(typeof(TEnum), parsedValue)
                : (parsedValue = default(TEnum)).Equals(default(TEnum)) && false;
        }
        public static OfferTypeInfo GetOfferingTypesInfo(string offeringType)
        {
            OfferTypeInfo offerTypeInfo = new();
            string[] enquiries = offeringType.Split(',');
            offerTypeInfo.OfferingType = OfferType.None;

            if (Enum.TryParse<OfferType>(offeringType.Trim(), true, out var enquiryType))
            {
                offerTypeInfo.OfferingType = enquiryType;
                offerTypeInfo.IsValidInfo = true;
            }
            else
            {
                offerTypeInfo.InvalidOfferingType = offeringType.Trim();
                offerTypeInfo.IsValidInfo = false;
            }

            return offerTypeInfo;
        }
        public static PurPoseType GetPurposeInfo(string purPoseType)
        {
            PurPoseType offerTypeInfo = new();
            string[] enquiries = purPoseType.Split(',');
            offerTypeInfo.Purpose = Purpose.None;

            if (Enum.TryParse<Purpose>(purPoseType.Trim(), true, out var enquiryType))
            {
                offerTypeInfo.Purpose = enquiryType;
                offerTypeInfo.IsValidInfo = true;
            }
            else
            {
                offerTypeInfo.InvalidOfferingType = purPoseType.Trim();
                offerTypeInfo.IsValidInfo = false;
            }

            return offerTypeInfo;
        }
        public static GenderType GetGenderInfo(string gender)
        {
            GenderType genderInfo = new();
            string[] genders = gender.Split(',');
            genderInfo.Gender = Gender.NotMentioned;

            if (Enum.TryParse<Gender>(gender.Trim(), true, out var parsedGender))
            {
                genderInfo.Gender = parsedGender;
                genderInfo.IsValidInfo = true;
            }
            else
            {
                genderInfo.InvalidGenderType = gender.Trim();
                genderInfo.IsValidInfo = false;
            }

            return genderInfo;
        }
        public static MaritalStatus GetMaritalStatusInfo(string maritalStatus)
        {
            MaritalStatus maritalStatusInfo = new();
            string[] statuses = maritalStatus.Split(',');
            maritalStatusInfo.MaritalStatusType = MaritalStatusType.NotMentioned;

            if (Enum.TryParse<MaritalStatusType>(maritalStatus.Trim(), true, out var parsedMaritalStatus))
            {
                maritalStatusInfo.MaritalStatusType = parsedMaritalStatus;
                maritalStatusInfo.IsValidInfo = true;
            }
            else
            {
                maritalStatusInfo.InvalidMaritalStatusType = maritalStatus.Trim();
                maritalStatusInfo.IsValidInfo = false;
            }

            return maritalStatusInfo;
        }
        public static DateOfBirthType GetDateOfBirthInfo(string dateOfBirth)
        {
            DateOfBirthType dobInfo = new();
            string[] dobEntries = dateOfBirth.Split(',');
            dobInfo.DateOfBirth = null;
            dobInfo.IsValidInfo = false;

            if (string.IsNullOrWhiteSpace(dateOfBirth))
            {
                dobInfo.InvalidDateOfBirth = dateOfBirth;
                return dobInfo;
            }

            if (DateTime.TryParseExact(dateOfBirth.Trim(), LeadMigrateHelper.formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDob)
                || DateTime.TryParse(dateOfBirth.Trim(), out parsedDob)) // Fallback
            {
                if (parsedDob.Date <= DateTime.Today)
                {
                    dobInfo.DateOfBirth = parsedDob.Date;
                    dobInfo.IsValidInfo = true;
                }
                else
                {
                    dobInfo.InvalidDateOfBirth = dateOfBirth.Trim();
                }
            }
            else
            {
                dobInfo.InvalidDateOfBirth = dateOfBirth.Trim();
            }

            return dobInfo;
        }
        public static ProfessionType GetProfessionInfo(string professionType)
        {
            ProfessionType offerTypeInfo = new();
            string[] enquiries = professionType.Split(',');
            offerTypeInfo.Profession = Profession.None;

            if (Enum.TryParse<Profession>(professionType.Trim(), true, out var enquiryType))
            {
                offerTypeInfo.Profession = enquiryType;
                offerTypeInfo.IsValidInfo = true;
            }
            else
            {
                offerTypeInfo.InvalidOfferingType = professionType.Trim();
                offerTypeInfo.IsValidInfo = false;
            }

            return offerTypeInfo;
        }
        /* public static LeadSourceInfo GetLeadSourceInfo(string inputSource)
         {
             var source = inputSource.Replace(" ", "");
             var leadSource = string.Empty;
             LeadSourceInfo leadSourceInfo = new();
             if (!string.IsNullOrEmpty(inputSource))
             {

                 try
                 {
                     List<string?> leadSources = typeof(Lrb.Domain.Constants.EnumDescription.LeadSource)
                    .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
                .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(string))
                .Select(f => (string)f.GetValue(null))
                .ToList();
                     foreach (var s in leadSources)
                     {
                         if ((s?.ToLower().Replace(" ", "").Contains(source.ToLower().Trim()) ?? false) || (source?.ToLower().Contains(s.ToLower().Replace(" ", "").Trim()) ?? false))
                         {
                             leadSource = s;
                             break;
                         }
                     }
                 }
                 catch (Exception e)
                 {
                     leadSource = source;
                 }
                 Domain.Enums.LeadSource? leadSourceValue = leadSource.GetValueFromDescription<Domain.Enums.LeadSource>();
                 if (Enum.TryParse<Domain.Enums.LeadSource>(leadSourceValue?.ToString(), true, out var sourc))
                 {
                     leadSourceInfo.LeadSource = sourc;
                     leadSourceInfo.IsValidInfo = true;
                     return leadSourceInfo;
                 }
                 else
                 {
                     leadSourceInfo.InvalidLeadSource = inputSource;
                     leadSourceInfo.IsValidInfo = false;
                     return leadSourceInfo;
                 }

             }
             else
             {
                 leadSourceInfo.InvalidLeadSource = inputSource;
                 leadSourceInfo.IsValidInfo = false;
                 return leadSourceInfo;
             }

         }*/
        public async static Task<LeadSourceInfo>? GetLeadSourceInfo(string inputSource, IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo)
        {
            var source = inputSource.Replace(" ", "").ToLower();
            var leadSource = string.Empty;
            LeadSourceInfo leadSourceInfo = new();

            if (!string.IsNullOrEmpty(inputSource))
            {
                try
                {
                    // Get list of lead sources from enum description
                    List<string?> leadSources = typeof(Lrb.Domain.Constants.EnumDescription.LeadSource)
                        .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)
                        .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(string))
                        .Select(f => (string)f.GetValue(null))
                        .ToList();

                    // Match the input source with the lead sources
                    foreach (var s in leadSources)
                    {
                        var normalizedLeadSource = s?.Replace(" ", "").ToLower();

                        if ((normalizedLeadSource?.Contains(source) ?? false) || (source.Contains(normalizedLeadSource ?? string.Empty)))
                        {
                            leadSource = s;
                            break;
                        }
                    }
                }
                catch (Exception)
                {
                    // Handle exceptions and set leadSource to the modified input source
                    leadSource = source;
                }

                // Get the enum value from the description
                Domain.Enums.LeadSource? leadSourceValue = leadSource.GetValueFromDescription<Domain.Enums.LeadSource>();

                // Try to parse the enum value
                if (Enum.TryParse<Domain.Enums.LeadSource>(leadSourceValue?.ToString(), true, out var sourc))
                {
                    bool isEnabled = await IsSourceEnabled((int)sourc, _sourceRepo);
                    if (isEnabled)
                    {
                        leadSourceInfo.LeadSource = sourc;
                        leadSourceInfo.IsValidInfo = true;
                        return leadSourceInfo;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            else
            {
                leadSourceInfo.InvalidLeadSource = inputSource;
                leadSourceInfo.IsValidInfo = false;
                return leadSourceInfo;
            }
        }
        private async static Task<bool> IsSourceEnabled(int value, IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRepo, CancellationToken cancellationToken = default)
        {
            try
            {
                var source = await _sourceRepo.FirstOrDefaultAsync(new GetSourceByValueSpecs(value), cancellationToken);

                if (source == null)
                {
                    return false;
                }
                if (source.IsEnabled == false) return false;
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        public static SubSourceInfo GetSubSource(List<SourceDto> subSources, LeadSource? leadSource, string subSource)
        {
            SubSourceInfo subSourceInfo = new();
            if (leadSource != null)
            {
                var sub = subSources.FirstOrDefault(i => i.SubSource?.ToLower()?.Trim() == subSource.ToLower().Trim() && i.LeadSource == leadSource)?.SubSource ?? string.Empty;
                if (string.IsNullOrEmpty(sub) && subSource != null)
                {
                    subSourceInfo.IsValidInfo = true;
                    subSourceInfo.SubSource = subSource;
                }
                else
                {
                    subSourceInfo.IsValidInfo = true;
                    subSourceInfo.SubSource = sub;
                }
            }
            else
            {
                subSourceInfo.IsValidInfo = false;
                subSourceInfo.SubSource = subSource;
            }
            return subSourceInfo;
        }
        public static string ConcatenatePhoneNumber(string? countryCodes, string? mobileNumber, Domain.Entities.GlobalSettings globalsettings)
        {
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalsettings?.CountriesInfo);
            var callingcode = countries?.FirstOrDefault()?.DefaultCallingCode ?? "91";
            int countryCode = 0;
            if (string.IsNullOrWhiteSpace(countryCodes))
            {

                countryCodes = callingcode;
            }
            try
            {
                countryCode = Convert.ToInt32(countryCodes);
            }
            catch
            {
                countryCodes = callingcode;
                countryCode = Convert.ToInt32(countryCodes);
            }

            mobileNumber = ValidateContactNumbers(mobileNumber, globalsettings, countryCode);
            if (string.IsNullOrWhiteSpace(mobileNumber))
            {
                return string.Empty;
            }
            return mobileNumber;
        }
        public static string V2ConcatenatePhoneNumber(this PhoneNumberUtil phoneUtil, string? countryCodes, string? mobileNumber, Domain.Entities.GlobalSettings globalSettings)
        {
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);

            if (string.IsNullOrWhiteSpace(mobileNumber))
                return string.Empty;

            int countryCode = 0;
            try
            {
                countryCode = string.IsNullOrWhiteSpace(countryCodes)
                    ? Convert.ToInt32(countries?.FirstOrDefault()?.DefaultCallingCode)
                    : Convert.ToInt32(countryCodes);
            }
            catch
            {
                return string.Empty; // Avoid fallback logic twice, return empty directly if conversion fails
            }

            return phoneUtil.V2ValidateContactNumbers(mobileNumber, globalSettings, countryCode) ?? string.Empty;
        }

        private static string? V2ValidateContactNumbers(this PhoneNumberUtil phoneUtil, string? contactNum, Domain.Entities.GlobalSettings globalSettings, int countryCodes)
        {
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);

            string countrycode = countryCodes.ToString();
            if (string.IsNullOrWhiteSpace(contactNum) || countryCodes <= 0)
                return string.Empty;
            //contactNum = Regex.Replace(contactNum, @"\s", "");
            contactNum = Regex.Replace(contactNum, @"[^\d+]", "");
            // Remove all non-numeric characters
            string mobileNumber = Regex.Replace(contactNum, "[^0-9]", "");

            // Quick validations
            if (mobileNumber.Length < 7 || mobileNumber.Length > 20)
                return string.Empty;
            if (contactNum.StartsWith("00"))
            {
                string numberWithoutZero = contactNum.Substring(2);
                contactNum = numberWithoutZero;
            }
            // Handle leading '0' for specific regions
            if (contactNum.StartsWith("0"))
            {
                // Remove the leading '0'
                string numberWithoutZero = contactNum.Substring(1);

                if (countryCodes == 91 && numberWithoutZero.Length == 10)
                {
                    return $"+{countryCodes}{numberWithoutZero}";
                }
                else if (countryCodes != 91)
                {
                    return $"+{countryCodes}{numberWithoutZero}";
                }
                else
                {
                    return string.Empty;
                }
            }
          
            if (contactNum.StartsWith("+") && mobileNumber.Length >= 7 && mobileNumber.Length <= 20)
                return contactNum; // Already formatted

            try
            {
                // Determine region and validate the number
                string defaultRegion = phoneUtil.GetRegionCodeForCountryCode(countryCodes) ?? countries?.FirstOrDefault()?.Code ?? string.Empty;
                if (string.IsNullOrWhiteSpace(defaultRegion))
                    return string.Empty;
                PhoneNumber phoneNumber = phoneUtil.Parse(mobileNumber, defaultRegion);
                PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
                string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
                string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
                if (defaultRegion == "AE")
                {
                    if (contactWithCountryCode.Length == 12)
                    {
                        return contactWithCountryCode;

                    }
                }
                if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
                {
                    return contactWithCountryCode;
                }
            }
            catch
            {
                return string.Empty; // Parsing failed
            }

            return string.Empty;
        }


        private static string? ValidateContactNumbers(string? conactnum, Domain.Entities.GlobalSettings globalsettings, int countrycodes)
        {
            try
            {
                var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalsettings?.CountriesInfo);

                string defaultRegion = null;
                string countrycode = countrycodes.ToString();
                string mobileNumber = Regex.Replace(conactnum, "[^0-9]", "");
                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
                if (conactnum.StartsWith("0") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
                {
                    mobileNumber = "+91" + mobileNumber.Substring(1);
                    return mobileNumber;
                }
                if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
                {
                    PhoneNumber number = phoneUtil.Parse("+" + mobileNumber, null);
                    defaultRegion = phoneUtil.GetRegionCodeForNumber(number);
                }
                if (string.IsNullOrWhiteSpace(defaultRegion))
                {
                    List<string> regionCodes = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap().GetValueOrDefault(countrycodes, new List<string>());
                    defaultRegion = regionCodes.FirstOrDefault();
                    if (string.IsNullOrWhiteSpace(defaultRegion))
                    {
                        defaultRegion = countries?.FirstOrDefault()?.Code;
                        countrycode = countries?.FirstOrDefault()?.DefaultCallingCode;
                    }
                }
                PhoneNumber phoneNumber = phoneUtil.Parse(mobileNumber, defaultRegion);
                PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
                string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
                string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
                string numericMobileNumber = Regex.Replace(formattedNumber, @"\D", "");
                if (defaultRegion == "AE")
                {
                    if (contactWithCountryCode.Length == 12)
                    {
                        return contactWithCountryCode;

                    }
                }

                bool isValid;
                if ((numericMobileNumber.Length == contactWithCountryCode.Length - 1))
                {
                    return contactWithCountryCode;

                }
                else if (conactnum.StartsWith("+") && mobileNumber.Length > 6 && mobileNumber.Length < 20)
                {
                    return "+" + mobileNumber;
                }
                else if (mobileNumber.Length > 6 && mobileNumber.Length < 20)
                {
                    return mobileNumber;
                }
                else
                {
                    return string.Empty;
                }

            }
            catch
            {
                return string.Empty;
            }
        }



        public static List<Domain.Entities.Lead> ConvertToLeads2(
              this DataTable table,
              Dictionary<DataColumns, string>? mappedColumnsData,
              List<string> unMappedColumns,
              List<Lrb.Domain.Entities.Project> projects,
              List<Domain.Entities.Property> properties,
              List<MasterPropertyType> propertyTypes,
              List<MasterAreaUnit> areaUnits,
              List<CustomMasterLeadStatus> leadStatuses,
              List<UserDetailsDto> users,
              List<SourceDto> subSources,
              List<IntegrationInfoDto> integrationInfo,
              List<Domain.Entities.Agency> agencies,
              Domain.Entities.GlobalSettings globalSettings,
              List<Domain.Entities.ChannelPartner> channelPartners,
             Domain.Entities.BulkLeadUploadTracker leadUploadTracker,
              List<Domain.Entities.Campaign> campaigns,
             string JsonData = null
             )
        {
            var newStatus = leadStatuses.FirstOrDefault(i => i.Status == "new");
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);

            List<Domain.Entities.Lead> leads = new();
            foreach (DataRow row in table.Rows)
            {
                string lowerBudget = !mappedColumnsData.ContainsKey(DataColumns.LowerBudget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.LowerBudget]) ? string.Empty : row[mappedColumnsData[DataColumns.LowerBudget]].ToString();
                string upperBudget = !mappedColumnsData.ContainsKey(DataColumns.UpperBudget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.UpperBudget]) ? string.Empty : row[mappedColumnsData[DataColumns.UpperBudget]].ToString();
                var beds = !mappedColumnsData.ContainsKey(DataColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Beds]) ? string.Empty : row[mappedColumnsData[DataColumns.Beds]].ToString();
                var baths = !mappedColumnsData.ContainsKey(DataColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Baths]) ? string.Empty : row[mappedColumnsData[DataColumns.Baths]].ToString();
                var furnishStatus = !mappedColumnsData.ContainsKey(DataColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[DataColumns.FurnishStatus]].ToString();
                string bHKTypes = !mappedColumnsData.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) ? string.Empty : row[mappedColumnsData[DataColumns.BHKType]].ToString();
                string noOfBHKs = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? string.Empty : row[mappedColumnsData[DataColumns.NoOfBHK]].ToString();
                string basePropertyType = !mappedColumnsData.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.BasePropertyType]].ToString();
                string subPropertyType = !mappedColumnsData.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.SubPropertyType]].ToString();
                string enquiryTypes = !mappedColumnsData.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.EnquiredFor]) ? string.Empty : row[mappedColumnsData[DataColumns.EnquiredFor]].ToString();
                string leadSource = !mappedColumnsData.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Source]) ? string.Empty : row[mappedColumnsData[DataColumns.Source]].ToString();
                string budget = !mappedColumnsData.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Budget]) ? string.Empty : row[mappedColumnsData[DataColumns.Budget]].ToString();
                string subSource = !mappedColumnsData.ContainsKey(DataColumns.SubSource) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubSource]) ? string.Empty : row[mappedColumnsData[DataColumns.SubSource]].ToString();
                string currecncy = !mappedColumnsData.ContainsKey(DataColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Currency]) ? string.Empty : row[mappedColumnsData[DataColumns.Currency]].ToString();
                string floor = !mappedColumnsData.ContainsKey(DataColumns.PreferredFloor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PreferredFloor]) ? string.Empty : row[mappedColumnsData[DataColumns.PreferredFloor]].ToString();

                var propertyInfo = GetPropertyType(basePropertyType, subPropertyType, propertyTypes, bHKTypes, noOfBHKs, beds, baths, furnishStatus, floor);
                var leadStatus = mappedColumnsData.GetLeadStatus(row, leadStatuses);
                string countryCode = !mappedColumnsData.ContainsKey(DataColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.CountryCode]].ToString();
                string altCountryCode = !mappedColumnsData.ContainsKey(DataColumns.AlternativeNoCountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternativeNoCountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.AlternativeNoCountryCode]].ToString();
                var offeringType = !mappedColumnsData.ContainsKey(DataColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[DataColumns.OfferingType]].ToString();
                var purpose = !mappedColumnsData.ContainsKey(DataColumns.Purpose) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Purpose]) ? string.Empty : row[mappedColumnsData[DataColumns.Purpose]].ToString();
                var scheduledDate = mappedColumnsData.GetDataTimeValue(DataColumns.ScheduledDate, row, JsonData);
                var currencycode = mappedColumnsData.GetCurrencySymbol1(row, currecncy, countries?.FirstOrDefault()?.DefaultCurrency);
                if (lowerBudget?.Contains('-') ?? false)
                {
                    string[] lowerbudgets = lowerBudget.Split('-');
                    lowerBudget = lowerbudgets.FirstOrDefault();
                }
                if (upperBudget?.Contains('-') ?? false)
                {
                    string[] upperbudgets = upperBudget.Split('-');
                    upperBudget = upperbudgets.LastOrDefault();
                }
                var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
                var upperBudgetinfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
                //var enquiryInfo = GetEnquiryForInfo(enquiredFor ?? string.Empty);
                //var leadsourceInfo = GetLeadSourceInfo(leadSource ?? string.Empty);
                //ar subSourceInfo = GetSubSource(subSources, leadsourceInfo.IsValidInfo ? leadsourceInfo.LeadSource : null, subSource ?? string.Empty);
                var enquiryTypesInfo = GetEnquiryTypesInfo(enquiryTypes ?? string.Empty);
                var offeringTypeInfo = GetOfferingTypesInfo(offeringType ?? string.Empty);
                var purposees = GetPurposeInfo(purpose ?? string.Empty);
                var agencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString();
                var agencyToAdd = agencies.FirstOrDefault(i => i.Name.Trim().Equals(agencyName?.Trim(), StringComparison.InvariantCultureIgnoreCase));
                var cpName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString();
                var channelPartenr = channelPartners.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var campaignName = !mappedColumnsData.ContainsKey(DataColumns.CampaignName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CampaignName]) ? null : row[mappedColumnsData[DataColumns.CampaignName]].ToString();
                var campaignToAdd = campaigns.FirstOrDefault(i => i.Name.Trim().Equals(campaignName?.Trim(), StringComparison.InvariantCultureIgnoreCase));
                var lead = new Domain.Entities.Lead()
                {
                    Name = !mappedColumnsData.ContainsKey(DataColumns.Name) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Name]) ? string.Empty : row[mappedColumnsData[DataColumns.Name]].ToString(),
                    ContactNo = !mappedColumnsData.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ContactNo]) ? string.Empty : row[mappedColumnsData[DataColumns.ContactNo]].ToString(),
                    Email = !mappedColumnsData.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Email]) ? string.Empty : row[mappedColumnsData[DataColumns.Email]].ToString(),
                    Rating = !mappedColumnsData.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Rating]) ? string.Empty : row[mappedColumnsData[DataColumns.Rating]].ToString(),
                    //CustomLeadStatus = newStatus,
                    AgencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString(),
                    Agencies = agencyToAdd != null
                            ? new List<Domain.Entities.Agency>() { agencyToAdd }
                             : !string.IsNullOrEmpty(agencyName)
                             ? new List<Domain.Entities.Agency>()
                                  {
                                        new Domain.Entities.Agency()
                                        {
                                            Name = agencyName,
                                            CreatedBy = leadUploadTracker.CreatedBy,
                                            LastModifiedBy = leadUploadTracker.LastModifiedBy
                                        }
                                  } : null,
                    ReferralName = !mappedColumnsData.ContainsKey(DataColumns.ReferralName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralName]) ? null : row[mappedColumnsData[DataColumns.ReferralName]].ToString(),
                    ReferralContactNo = !mappedColumnsData.ContainsKey(DataColumns.ReferralContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralContactNo]) ? null : row[mappedColumnsData[DataColumns.ReferralContactNo]].ToString(),
                    ReferralEmail = !mappedColumnsData.ContainsKey(DataColumns.ReferralEmail) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralEmail]) ? null : row[mappedColumnsData[DataColumns.ReferralEmail]].ToString(),

                    Enquiries = new List<LeadEnquiry>() {
                    new(){
                        IsPrimary = true,
                        PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                        PropertyTypes = propertyInfo.IsValidInfo ? propertyInfo.PropertyTypes : default,
                        //BHKType = propertyInfo.IsValidInfo ? propertyInfo.BHKType : default,
                       // NoOfBHKs = propertyInfo.IsValidInfo ? propertyInfo.NoOfBHK : default,
                        //EnquiredFor = enquiryInfo.IsValidInfo ? enquiryInfo.EnquiredFor : default,
                        //LeadSource = leadsourceInfo.IsValidInfo ? leadsourceInfo.LeadSource: default,
                        //SubSource =  subSourceInfo.IsValidInfo ? subSourceInfo.SubSource: default,
                        UpperBudget = upperBudgetinfo.IsValidInfo ? upperBudgetinfo.Budget : default,
                        LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : default,
                        Currency = currencycode ?? "INR",
                        BHKTypes=propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                        BHKs=propertyInfo.IsValidInfo ? propertyInfo.BHKs : default,
                        EnquiryTypes=enquiryTypesInfo.IsValidInfo ? enquiryTypesInfo.EnquiryTypes : default,
                        Beds=propertyInfo.IsValidInfo ? propertyInfo.Beds : default,
                        Baths=propertyInfo.IsValidInfo ? propertyInfo.Baths : default,
                        Furnished=propertyInfo.IsValidInfo ? propertyInfo.Furnished : default,
                        Floors =propertyInfo.IsValidInfo ? propertyInfo.Floors :default,
                        Addresses = new()
                        {
                            new()
                            {
                             City = !mappedColumnsData.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.City]) ? string.Empty : row[mappedColumnsData[DataColumns.City]].ToString(),
                             State = !mappedColumnsData.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.State]) ? string.Empty : row[mappedColumnsData[DataColumns.State]].ToString(),
                             SubLocality = !mappedColumnsData.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Location]) ? string.Empty : row[mappedColumnsData[DataColumns.Location]].ToString(),
                             Community = !mappedColumnsData.ContainsKey(DataColumns.Community) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Community]) ? string.Empty : row[mappedColumnsData[DataColumns.Community]].ToString(),
                             SubCommunity = !mappedColumnsData.ContainsKey(DataColumns.SubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.SubCommunity]].ToString(),
                             TowerName = !mappedColumnsData.ContainsKey(DataColumns.TowerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.TowerName]) ? string.Empty : row[mappedColumnsData[DataColumns.TowerName]].ToString(),
                             Country = !mappedColumnsData.ContainsKey(DataColumns.Country) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Country]) ? string.Empty : row[mappedColumnsData[DataColumns.Country]].ToString(),
                             PostalCode = !mappedColumnsData.ContainsKey(DataColumns.PostalCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PostalCode]) ? string.Empty : row[mappedColumnsData[DataColumns.PostalCode]].ToString(),
                            }
                        },
                        OfferType = offeringTypeInfo.IsValidInfo ? offeringTypeInfo.OfferingType :OfferType.None,
                        Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None,
                    } },
                    Projects = !mappedColumnsData.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Project]) || string.IsNullOrWhiteSpace(row[mappedColumnsData[DataColumns.Project]]?.ToString()) ? null : new List<Lrb.Domain.Entities.Project>() { projects.FirstOrDefault(i => i.Name?.ToLower().Trim() == (!mappedColumnsData.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Project]) ? default : row[mappedColumnsData[DataColumns.Project]].ToString()?.ToLower().Trim())) },
                    Properties = !mappedColumnsData.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Property]) || string.IsNullOrWhiteSpace(row[mappedColumnsData[DataColumns.Property]].ToString()) ? new List<Domain.Entities.Property>() : new List<Domain.Entities.Property>() { properties.FirstOrDefault(i => i.Title?.ToLower().Trim() == (!mappedColumnsData.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Property]) ? default : row[mappedColumnsData[DataColumns.Property]].ToString()?.ToLower().Trim())) },
                    AlternateContactNo = !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternateContactNo]) ? null : row[mappedColumnsData[DataColumns.AlternateContactNo]].ToString().Trim(),
                    Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                    Designation = !mappedColumnsData.ContainsKey(DataColumns.Designation) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Designation]) ? string.Empty : row[mappedColumnsData[DataColumns.Designation]].ToString(),
                    ChannelPartnerExecutiveName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerExecutiveName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]].ToString(),
                    ChannelPartnerName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString(),
                    ChannelPartnerContactNo = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerContactNo]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerContactNo]].ToString().Trim(),
                    ScheduledDate = scheduledDate,
                    CustomLeadStatus = leadStatus ?? leadStatuses.FirstOrDefault(i => i.Status == "new"),
                    CompanyName = !mappedColumnsData.ContainsKey(DataColumns.CompanyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CompanyName]) ? string.Empty : row[mappedColumnsData[DataColumns.CompanyName]].ToString(),
                    ChannelPartners = channelPartenr != null
                            ? new List<Domain.Entities.ChannelPartner>() { channelPartenr }
                             : !string.IsNullOrEmpty(cpName)
                             ? new List<Domain.Entities.ChannelPartner>()
                                  {
                                        new Domain.Entities.ChannelPartner()
                                        {
                                            FirmName = cpName,
                                            CreatedBy = leadUploadTracker.CreatedBy,
                                            LastModifiedBy = leadUploadTracker.LastModifiedBy
                                        }
                                  } : null,
                    Campaigns = campaignToAdd != null
                            ? new List<Domain.Entities.Campaign>() { campaignToAdd }
                             : !string.IsNullOrEmpty(campaignName)
                             ? new List<Domain.Entities.Campaign>()
                                  {
                                        new Domain.Entities.Campaign()
                                        {
                                            Name = campaignName,
                                            CreatedBy = leadUploadTracker.CreatedBy,
                                            LastModifiedBy = leadUploadTracker.LastModifiedBy
                                        }
                                  } : null,
                    BulkCategory = BulkType.BulkUpload,

                    CountryCode = countryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                    AltCountryCode = altCountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",


                };

                #region UpdateSubsource
                //if (!subSourceInfo.IsValidInfo && leadsourceInfo.IsValidInfo)
                //{
                //    if (!string.IsNullOrEmpty(subSourceInfo.SubSource) &&
                //        leadsourceInfo.LeadSource != LeadSource.Facebook &&
                //        leadsourceInfo.LeadSource != LeadSource.GoogleAds &&
                //        leadsourceInfo.LeadSource != LeadSource.Gmail)
                //    {
                //        try
                //        {
                //            integrationInfo.Add(new IntegrationInfoDto { LeadId = lead.Id, AccountName = subSourceInfo.SubSource, Source = leadsourceInfo.LeadSource });
                //            subSources.Add(new SourceDto { LeadSource = leadsourceInfo.LeadSource, SubSource = subSourceInfo.SubSource });
                //        }
                //        catch (Exception ex)
                //        {
                //            throw;
                //        }
                //    }
                //}
                #endregion
                if (!propertyInfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
                }
                if (!upperBudgetinfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(upperBudgetinfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetinfo.Invalidbudget : string.Empty;
                }
                if (!lowerBudgetInfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
                }
                //if (!leadsourceInfo.IsValidInfo)
                //{
                //    lead.Notes += !string.IsNullOrEmpty(leadsourceInfo.InvalidLeadSource) ? ", \n" + "LeadSource" + " - " + leadsourceInfo.InvalidLeadSource : string.Empty;
                //}
                //if (!enquiryTypesInfo.IsValidInfo)
                //{
                //    lead.Notes += !string.IsNullOrEmpty(enquiryTypesInfo.InvalidEnquiredFor) ? ", \n" + "EnquiryFOr" + " - " + enquiryTypesInfo.InvalidEnquiredFor : string.Empty;
                //}
                //if (!subSourceInfo.IsValidInfo)
                //{
                //    lead.Notes += !string.IsNullOrEmpty(subSourceInfo.SubSource) ? ", \n" + "subSource" + " - " + subSourceInfo.SubSource : string.Empty;
                //}
                if (!leadStatus.IsValidStatus(leadStatuses))
                {
                    var baseStatusString = mappedColumnsData.GetStringValue(DataColumns.BaseStatus, row);
                    var subStatusString = mappedColumnsData.GetStringValue(DataColumns.SubStatus, row);

                    lead.Notes += !string.IsNullOrWhiteSpace(baseStatusString) ? $"\n{mappedColumnsData[DataColumns.BaseStatus]} - {baseStatusString}" : string.Empty;
                    lead.Notes += !string.IsNullOrWhiteSpace(subStatusString) ? $"\n{mappedColumnsData[DataColumns.SubStatus]} - {subStatusString}" : string.Empty;
                }


                leads.Add(lead);
            }
            return leads;
        }


        public static (List<Domain.Entities.Lead>, List<V2InvalidData>) ConvertToLeadsV3(
        this DataTable table,
        Dictionary<DataColumns, string>? mappedColumnsData,
        List<string> unMappedColumns,
        MasterItems masterItems,
        BulkLeadUploadTracker leadUploadTracker,
        IRepositoryWithEvents<Lrb.Domain.Entities.Source> _sourceRep,
        string JsonData = null)
        {
            var newStatus = masterItems.LeadStatuses?.FirstOrDefault(i => i.Status == "new");
            List<Domain.Entities.Lead> leads = new();
            List<V2InvalidData> invalidLeads = new();
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();
            Guid? defaultUnitId = Guid.TryParse(masterItems?.GlobalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(masterItems.GlobalSettings?.CountriesInfo);

            foreach (DataRow row in table.Rows)
            {
                string lowerBudget = !mappedColumnsData.ContainsKey(DataColumns.LowerBudget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.LowerBudget]) ? string.Empty : row[mappedColumnsData[DataColumns.LowerBudget]].ToString();
                string upperBudget = !mappedColumnsData.ContainsKey(DataColumns.UpperBudget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.UpperBudget]) ? string.Empty : row[mappedColumnsData[DataColumns.UpperBudget]].ToString();
                var beds = !mappedColumnsData.ContainsKey(DataColumns.Beds) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Beds]) ? string.Empty : row[mappedColumnsData[DataColumns.Beds]].ToString();
                var baths = !mappedColumnsData.ContainsKey(DataColumns.Baths) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Baths]) ? string.Empty : row[mappedColumnsData[DataColumns.Baths]].ToString();
                var furnishStatus = !mappedColumnsData.ContainsKey(DataColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[DataColumns.FurnishStatus]].ToString();
                string floor = !mappedColumnsData.ContainsKey(DataColumns.PreferredFloor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PreferredFloor]) ? string.Empty : row[mappedColumnsData[DataColumns.PreferredFloor]].ToString();
                string bHKTypes = !mappedColumnsData.ContainsKey(DataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BHKType]) ? string.Empty : row[mappedColumnsData[DataColumns.BHKType]].ToString();
                string noOfBHKs = !mappedColumnsData.ContainsKey(DataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NoOfBHK]) ? string.Empty : row[mappedColumnsData[DataColumns.NoOfBHK]].ToString();
                string basePropertyType = !mappedColumnsData.ContainsKey(DataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.BasePropertyType]].ToString();
                string subPropertyType = !mappedColumnsData.ContainsKey(DataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[DataColumns.SubPropertyType]].ToString();
                string enquiryTypes = !mappedColumnsData.ContainsKey(DataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.EnquiredFor]) ? string.Empty : row[mappedColumnsData[DataColumns.EnquiredFor]].ToString();
                string leadSource = !mappedColumnsData.ContainsKey(DataColumns.Source) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Source]) ? string.Empty : row[mappedColumnsData[DataColumns.Source]].ToString();
                string budget = !mappedColumnsData.ContainsKey(DataColumns.Budget) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Budget]) ? string.Empty : row[mappedColumnsData[DataColumns.Budget]].ToString();
                string subSource = !mappedColumnsData.ContainsKey(DataColumns.SubSource) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubSource]) ? string.Empty : row[mappedColumnsData[DataColumns.SubSource]].ToString();
                string currecncy = !mappedColumnsData.ContainsKey(DataColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Currency]) ? string.Empty : row[mappedColumnsData[DataColumns.Currency]].ToString();
                var offeringType = !mappedColumnsData.ContainsKey(DataColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[DataColumns.OfferingType]].ToString();
                var propertyInfo = GetPropertyType(basePropertyType, subPropertyType, masterItems.PropetyTypes, bHKTypes, noOfBHKs, beds, baths, furnishStatus, floor);
                var leadStatus = mappedColumnsData.GetLeadStatus(row, masterItems.LeadStatuses);
                var user = mappedColumnsData.GetStringValue(DataColumns.AssignToUser, row);
                var assignToUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                var secondaryUser = mappedColumnsData.GetStringValue(DataColumns.AssignToSecondaryUser, row);
                var assignToSecondaryUser = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == secondaryUser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted && user.ToLower().Trim().Replace(" ", "") != secondaryUser.ToLower().Trim().Replace(" ", ""));
                string countryCode = !mappedColumnsData.ContainsKey(DataColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.CountryCode]].ToString();
                string altCountryCode = !mappedColumnsData.ContainsKey(DataColumns.AlternativeNoCountryCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternativeNoCountryCode]) ? string.Empty : row[mappedColumnsData[DataColumns.AlternativeNoCountryCode]].ToString();
                var scheduledDate = mappedColumnsData.GetDataTimeValue(DataColumns.ScheduledDate, row, JsonData);
                var currencycode = mappedColumnsData.GetCurrencySymbol1(row, currecncy, countries?.FirstOrDefault()?.DefaultCurrency);
                string? carpetArea = !mappedColumnsData.ContainsKey(DataColumns.CarpetArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CarpetArea]) ? string.Empty : row[mappedColumnsData[DataColumns.CarpetArea]].ToString();
                string? carpetAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.CarpetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CarpetAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.CarpetAreaUnit]].ToString();
                var carpetarea = GetUnitDetails(carpetArea ?? string.Empty, masterItems.AreaUnits, carpetAreaUnit ?? string.Empty, defaultUnitId);
                string? propertyArea = !mappedColumnsData.ContainsKey(DataColumns.PropertyArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PropertyArea]) ? string.Empty : row[mappedColumnsData[DataColumns.PropertyArea]].ToString();
                string? propertyAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.PropertyAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PropertyAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.PropertyAreaUnit]].ToString();
                var propertyArea1 = GetUnitDetails(propertyArea ?? string.Empty, masterItems.AreaUnits, propertyAreaUnit ?? string.Empty, defaultUnitId);
                string? netArea = !mappedColumnsData.ContainsKey(DataColumns.NetArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NetArea]) ? string.Empty : row[mappedColumnsData[DataColumns.NetArea]].ToString();
                string? netAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.NetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.NetAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.NetAreaUnit]].ToString();
                var netArea1 = GetUnitDetails(netArea ?? string.Empty, masterItems.AreaUnits, netAreaUnit ?? string.Empty, defaultUnitId);
                string? builtUpArea = !mappedColumnsData.ContainsKey(DataColumns.BuiltUpArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BuiltUpArea]) ? string.Empty : row[mappedColumnsData[DataColumns.BuiltUpArea]].ToString();
                string? builtUpAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.BuiltUpAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.BuiltUpAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.BuiltUpAreaUnit]].ToString();
                var builtUpArea1 = GetUnitDetails(builtUpArea ?? string.Empty, masterItems.AreaUnits, builtUpAreaUnit ?? string.Empty, defaultUnitId);
                string? SaleableArea = !mappedColumnsData.ContainsKey(DataColumns.SaleableArea) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SaleableArea]) ? string.Empty : row[mappedColumnsData[DataColumns.SaleableArea]].ToString();
                string? SaleableAreaUnit = !mappedColumnsData.ContainsKey(DataColumns.SaleableAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SaleableAreaUnit]) ? string.Empty : row[mappedColumnsData[DataColumns.SaleableAreaUnit]].ToString();
                var SaleableArea1 = GetUnitDetails(SaleableArea ?? string.Empty, masterItems.AreaUnits, SaleableAreaUnit ?? string.Empty);
                var purpose = !mappedColumnsData.ContainsKey(DataColumns.Purpose) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Purpose]) ? string.Empty : row[mappedColumnsData[DataColumns.Purpose]].ToString();

                if (lowerBudget?.Contains('-') ?? false)
                {
                    string[] lowerbudgets = lowerBudget.Split('-');
                    lowerBudget = lowerbudgets.FirstOrDefault();
                }
                if (upperBudget?.Contains('-') ?? false)
                {
                    string[] upperbudgets = upperBudget.Split('-');
                    upperBudget = upperbudgets.LastOrDefault();
                }
                var lowerBudgetInfo = BudgetHelper.ConvertBuget(lowerBudget ?? string.Empty);
                var upperBudgetinfo = BudgetHelper.ConvertBuget(upperBudget ?? string.Empty);
                //var enquiryInfo = GetEnquiryForInfo(enquiredFor ?? string.Empty);
                var leadsourceInfo = GetLeadSourceInfo(leadSource ?? string.Empty, _sourceRep);
                SubSourceInfo? subSourceInfo = new();
                if (leadsourceInfo?.Result != null)
                {
                    subSourceInfo = GetSubSource(masterItems.SubSources, leadsourceInfo.Result.IsValidInfo ? leadsourceInfo.Result.LeadSource : null, subSource ?? string.Empty);
                }
                var enquiryTypesInfo = GetEnquiryTypesInfo(enquiryTypes ?? string.Empty);
                var offeringTypeInfo = GetOfferingTypesInfo(offeringType ?? string.Empty);
                var purposees = GetPurposeInfo(purpose ?? string.Empty);
                var projectName = !mappedColumnsData.ContainsKey(DataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Project]) ? null : row[mappedColumnsData[DataColumns.Project]].ToString();
                var project = masterItems.Projects?.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(projectName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var propertyName = !mappedColumnsData.ContainsKey(DataColumns.Property) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Property]) ? null : row[mappedColumnsData[DataColumns.Property]].ToString();
                var property = masterItems.Properties?.FirstOrDefault(i => i.Title.Trim().ToLower().Equals(propertyName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var agencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString();
                var agencyToAdd = masterItems.Agencies?.FirstOrDefault(i => i.Name.Replace(" ", "").Trim().Equals(agencyName?.Replace(" ", "").Trim(), StringComparison.InvariantCultureIgnoreCase));
                var cpName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString();
                var channelPartenr = masterItems.ChannelPartners?.FirstOrDefault(i => i.FirmName.Trim().ToLower().Equals(cpName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var campaignName = !mappedColumnsData.ContainsKey(DataColumns.CampaignName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CampaignName]) ? null : row[mappedColumnsData[DataColumns.CampaignName]].ToString();
                var campaign = masterItems.Campaigns?.FirstOrDefault(i => i.Name.Trim().ToLower().Equals(campaignName?.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase));
                var sourcinguser = mappedColumnsData.GetStringValue(DataColumns.SourcingManager, row);
                var sourcinguserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == sourcinguser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                var ClosingUser = mappedColumnsData.GetStringValue(DataColumns.ClosingManager, row);
                var ClosingUserName = masterItems.Users?.FirstOrDefault(u => u.UserName.ToLower() == ClosingUser.ToLower().Trim().Replace(" ", "") && !u.IsDeleted);
                var profession = !mappedColumnsData.ContainsKey(DataColumns.Profession) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Profession]) ? string.Empty : row[mappedColumnsData[DataColumns.Profession]].ToString();
                var validProfession = GetProfessionInfo(profession ?? string.Empty);
                var possesionType = !mappedColumnsData.ContainsKey(DataColumns.PossesionType) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PossesionType]) ? string.Empty : row[mappedColumnsData[DataColumns.PossesionType]].ToString();
                var possessionDate = mappedColumnsData.GetPossessionDate(DataColumns.PossessionDate, row, JsonData);
                bool isValidPossessionDate = possessionDate.HasValue;
                DateTime? dateTime = possessionDate;
                if (possesionType != "None" && possesionType != "CustomDate")
                {
                    DateTime currentUtcTime = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month));
                    possessionDate = currentUtcTime;
                    isValidPossessionDate = true;

                    if (possesionType == "UnderConstruction")
                    {
                        dateTime = null;
                    }
                    else if (possesionType == "SixMonth")
                    {
                        dateTime = currentUtcTime.AddMonths(6);
                    }
                    else if (possesionType == "Year")
                    {
                        dateTime = currentUtcTime.AddYears(1);
                    }
                    else if (possesionType == "TwoYears")
                    {
                        dateTime = currentUtcTime.AddYears(2);
                    }
                }
                if(dateTime.HasValue && (possesionType == null || possesionType=="None" || string.IsNullOrWhiteSpace(possesionType)))
                {
                    possesionType = PossesionType.CustomDate.ToString();
                }

                string landline = !mappedColumnsData.ContainsKey(DataColumns.LandLine) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.LandLine]) ? string.Empty : row[mappedColumnsData[DataColumns.LandLine]].ToString();
               

                var gender = !mappedColumnsData.ContainsKey(DataColumns.Gender) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Gender]) ? string.Empty : row[mappedColumnsData[DataColumns.Gender]].ToString();
                var validGenderType = GetGenderInfo(gender ?? string.Empty);
                var maritalStatus = !mappedColumnsData.ContainsKey(DataColumns.MaritalStatus) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.MaritalStatus]) ? string.Empty : row[mappedColumnsData[DataColumns.MaritalStatus]].ToString();
                var validMaritalStatusType = GetMaritalStatusInfo(maritalStatus ?? string.Empty);
                var dateOdBirth = !mappedColumnsData.ContainsKey(DataColumns.DateOfBirth) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.DateOfBirth]) ? string.Empty : row[mappedColumnsData[DataColumns.DateOfBirth]].ToString();
                var validDateOfBirth = mappedColumnsData.GetDateOfBirth(DataColumns.DateOfBirth, row, JsonData);
                var lead = new Domain.Entities.Lead()
                {
                    Id = Guid.NewGuid(),
                    Name = !mappedColumnsData.ContainsKey(DataColumns.Name) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Name]) ? string.Empty : row[mappedColumnsData[DataColumns.Name]].ToString(),
                    ContactNo = !mappedColumnsData.ContainsKey(DataColumns.ContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ContactNo]) ? string.Empty : row[mappedColumnsData[DataColumns.ContactNo]].ToString(),
                    Email = !mappedColumnsData.ContainsKey(DataColumns.Email) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Email]) ? string.Empty : row[mappedColumnsData[DataColumns.Email]].ToString(),
                    Rating = !mappedColumnsData.ContainsKey(DataColumns.Rating) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Rating]) ? string.Empty : row[mappedColumnsData[DataColumns.Rating]].ToString(),
                    AgencyName = !mappedColumnsData.ContainsKey(DataColumns.AgencyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AgencyName]) ? null : row[mappedColumnsData[DataColumns.AgencyName]].ToString(),
                    Agencies = agencyToAdd != null ? new List<Domain.Entities.Agency>() { agencyToAdd } : null,
                    ReferralName = !mappedColumnsData.ContainsKey(DataColumns.ReferralName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralName]) ? null : row[mappedColumnsData[DataColumns.ReferralName]].ToString(),
                    ReferralContactNo = !mappedColumnsData.ContainsKey(DataColumns.ReferralContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralContactNo]) ? null : row[mappedColumnsData[DataColumns.ReferralContactNo]].ToString(),
                    AssignTo = assignToUser?.Id ?? Guid.Empty,
                    SecondaryUserId = assignToSecondaryUser?.Id,
                    UploadType = UploadType.Excel,
                    UploadTypeName = leadUploadTracker.S3BucketKey,
                    ReferralEmail = !mappedColumnsData.ContainsKey(DataColumns.ReferralEmail) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ReferralEmail]) ? null : row[mappedColumnsData[DataColumns.ReferralEmail]].ToString(),
                    Enquiries = new List<LeadEnquiry>()
                    {
                        new(){
                        Id = Guid.NewGuid(),
                        IsPrimary = true,
                        PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                        PropertyTypes = propertyInfo.IsValidInfo ? propertyInfo.PropertyTypes : default,
                        //LeadSource = leadsourceInfo.Result.IsValidInfo ? leadsourceInfo.Result.LeadSource: default,
                        //SubSource = subSourceInfo.IsValidInfo? (!string.IsNullOrWhiteSpace(subSourceInfo.SubSource) ? subSourceInfo.SubSource : null): null,
                        UpperBudget = upperBudgetinfo.IsValidInfo ? upperBudgetinfo.Budget : default,
                        LowerBudget = lowerBudgetInfo.IsValidInfo ? lowerBudgetInfo.Budget : default,
                        Currency = currencycode ?? "INR",
                        BHKTypes=propertyInfo.IsValidInfo ? propertyInfo.BHKTypes : default,
                        BHKs=propertyInfo.IsValidInfo ? propertyInfo.BHKs : default,
                        Beds=propertyInfo.IsValidInfo ? propertyInfo.Beds : default,
                        Baths=propertyInfo.IsValidInfo ? propertyInfo.Baths : default,
                        Furnished=propertyInfo.IsValidInfo ? propertyInfo.Furnished : default,
                        Floors =propertyInfo.IsValidInfo ? propertyInfo.Floors :default,
                        EnquiryTypes=enquiryTypesInfo.IsValidInfo ? enquiryTypesInfo.EnquiryTypes : default,
                        Addresses = ((mappedColumnsData.ContainsKey(DataColumns.City) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.City]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.City]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.State) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.State]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.State]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.Location) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.Location]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.Location]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.Community) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.Community]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.Community]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.SubCommunity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubCommunity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.SubCommunity]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.TowerName) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.TowerName]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.TowerName]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.Country) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.Country]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.Country]].ToString()))
                        ||(mappedColumnsData.ContainsKey(DataColumns.PostalCode) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.PostalCode]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.PostalCode]].ToString()))) ? new()
                        {
                            new()
                            {
                             Id =  Guid.NewGuid(),
                             City = !mappedColumnsData.ContainsKey(DataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.City]) ? string.Empty : row[mappedColumnsData[DataColumns.City]].ToString(),
                             State = !mappedColumnsData.ContainsKey(DataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.State]) ? string.Empty : row[mappedColumnsData[DataColumns.State]].ToString(),
                             SubLocality = !mappedColumnsData.ContainsKey(DataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Location]) ? string.Empty : row[mappedColumnsData[DataColumns.Location]].ToString(),
                             Community = !mappedColumnsData.ContainsKey(DataColumns.Community) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Community]) ? string.Empty : row[mappedColumnsData[DataColumns.Community]].ToString(),
                             SubCommunity = !mappedColumnsData.ContainsKey(DataColumns.SubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.SubCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.SubCommunity]].ToString(),
                             TowerName = !mappedColumnsData.ContainsKey(DataColumns.TowerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.TowerName]) ? string.Empty : row[mappedColumnsData[DataColumns.TowerName]].ToString(),
                             Country = !mappedColumnsData.ContainsKey(DataColumns.Country) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Country]) ? string.Empty : row[mappedColumnsData[DataColumns.Country]].ToString(),
                             PostalCode = !mappedColumnsData.ContainsKey(DataColumns.PostalCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.PostalCode]) ? string.Empty : row[mappedColumnsData[DataColumns.PostalCode]].ToString(),
                            }
                        } : null,
                         CarpetArea =  carpetarea.Item1,
                         CarpetAreaUnitId = carpetarea.Item2,
                         PropertyArea =  propertyArea1.Item1,
                         PropertyAreaUnitId = propertyArea1.Item2,
                         NetArea =  netArea1.Item1,
                         NetAreaUnitId = netArea1.Item2,
                         BuiltUpArea =  builtUpArea1.Item1,
                         BuiltUpAreaUnitId = builtUpArea1.Item2,
                         SaleableArea =  SaleableArea1.Item1,
                         SaleableAreaUnitId = SaleableArea1.Item2,
                        OfferType = offeringTypeInfo.IsValidInfo ? offeringTypeInfo.OfferingType :OfferType.None,
                        UnitName = !mappedColumnsData.ContainsKey(DataColumns.UnitName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.UnitName]) ? null : row[mappedColumnsData[DataColumns.UnitName]].ToString(),
                        ClusterName = !mappedColumnsData.ContainsKey(DataColumns.ClusterName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ClusterName]) ? null : row[mappedColumnsData[DataColumns.ClusterName]].ToString(),
                        Purpose = purposees.IsValidInfo ? purposees.Purpose :Purpose.None,
                        PossessionDate = isValidPossessionDate  ? dateTime?.ToUniversalTime() : null,
                        }
                    },
                        Projects = project != null ? new List<Domain.Entities.Project>() { project } : null,
                        Properties = property != null ? new List<Domain.Entities.Property>() { property } : null,
                        AlternateContactNo = !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.AlternateContactNo]) ? null : row[mappedColumnsData[DataColumns.AlternateContactNo]].ToString().Trim(),
                        Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                        Designation = !mappedColumnsData.ContainsKey(DataColumns.Designation) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Designation]) ? string.Empty : row[mappedColumnsData[DataColumns.Designation]].ToString(),
                        ChannelPartnerExecutiveName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerExecutiveName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerExecutiveName]].ToString(),
                        ChannelPartnerName = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerName]) ? string.Empty : row[mappedColumnsData[DataColumns.ChannelPartnerName]].ToString(),
                        ChannelPartnerContactNo = !mappedColumnsData.ContainsKey(DataColumns.ChannelPartnerContactNo) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.ChannelPartnerContactNo]) ? null : row[mappedColumnsData[DataColumns.ChannelPartnerContactNo]].ToString().Trim(),
                        ScheduledDate = scheduledDate,
                        CustomLeadStatus = leadStatus ?? newStatus,
                        CompanyName = !mappedColumnsData.ContainsKey(DataColumns.CompanyName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CompanyName]) ? string.Empty : row[mappedColumnsData[DataColumns.CompanyName]].ToString(),
                        ChannelPartners = channelPartenr != null ? new List<Domain.Entities.ChannelPartner>() { channelPartenr } : null,
                        BulkCategory = BulkType.BulkUpload,
                        CountryCode = countryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                        AltCountryCode = altCountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91",
                        CreatedOn = DateTime.UtcNow,
                        LastModifiedOn = DateTime.UtcNow,
                        Nationality = !mappedColumnsData.ContainsKey(DataColumns.Nationality) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.Nationality]) ? null : row[mappedColumnsData[DataColumns.Nationality]].ToString(),
                        Campaigns = campaign != null ? new List<Domain.Entities.Campaign>() { campaign } : null,
                        Address = ((mappedColumnsData.ContainsKey(DataColumns.CustomerCity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerCity]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerState) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerState]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerState]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerLocation) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerLocation]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerLocation]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerCommunity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCommunity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerCommunity]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerSubCommunity) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerSubCommunity]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerSubCommunity]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerTowerName) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerTowerName]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerTowerName]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerCountry) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCountry]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerCountry]].ToString()))
                        || (mappedColumnsData.ContainsKey(DataColumns.CustomerPostalCode) && !string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerPostalCode]) && !string.IsNullOrEmpty(row[mappedColumnsData[DataColumns.CustomerPostalCode]].ToString()))) ?
                        new Address
                        {
                            Id = Guid.NewGuid(),
                            City = !mappedColumnsData.ContainsKey(DataColumns.CustomerCity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCity]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerCity]].ToString(),
                            State = !mappedColumnsData.ContainsKey(DataColumns.CustomerState) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerState]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerState]].ToString(),
                            SubLocality = !mappedColumnsData.ContainsKey(DataColumns.CustomerLocation) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerLocation]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerLocation]].ToString(),
                            Community = !mappedColumnsData.ContainsKey(DataColumns.CustomerCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerCommunity]].ToString(),
                            SubCommunity = !mappedColumnsData.ContainsKey(DataColumns.CustomerSubCommunity) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerSubCommunity]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerSubCommunity]].ToString(),
                            TowerName = !mappedColumnsData.ContainsKey(DataColumns.CustomerTowerName) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerTowerName]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerTowerName]].ToString(),
                            Country = !mappedColumnsData.ContainsKey(DataColumns.CustomerCountry) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerCountry]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerCountry]].ToString(),
                            PostalCode = !mappedColumnsData.ContainsKey(DataColumns.CustomerPostalCode) || string.IsNullOrEmpty(mappedColumnsData[DataColumns.CustomerPostalCode]) ? string.Empty : row[mappedColumnsData[DataColumns.CustomerPostalCode]].ToString(),
                        } : null,
                        SourcingManager = sourcinguserName?.Id ?? Guid.Empty,
                        ClosingManager = ClosingUserName?.Id ?? Guid.Empty,
                        Profession = validProfession.IsValidInfo ? validProfession.Profession : Profession.None,
                        PossesionType = Enum.TryParse(possesionType, true, out PossesionType parsedType) && (parsedType != PossesionType.CustomDate || isValidPossessionDate) ? parsedType : default,
                        LandLine =(!string.IsNullOrWhiteSpace(landline) && Regex.IsMatch(Regex.Replace(landline, @"[^0-9\-]", ""), @"^[0-9\-]{6,16}$"))? Regex.Replace(landline, @"[^0-9\-]", ""): string.Empty,
                        Gender = validGenderType.IsValidInfo ? validGenderType.Gender : Gender.NotMentioned,
                        MaritalStatus = validMaritalStatusType.IsValidInfo ? validMaritalStatusType.MaritalStatusType : MaritalStatusType.NotMentioned,
                        DateOfBirth = validDateOfBirth ?? null

                };
                V2InvalidData invalidLead = null;
                bool isInvalidContact = false;
                if (!string.IsNullOrEmpty(lead.ContactNo))
                {
                    var contactNo = phoneUtil.V2ConcatenatePhoneNumber(lead.CountryCode, lead.ContactNo, masterItems.GlobalSettings ?? new());
                    if (string.IsNullOrEmpty(contactNo))
                    {
                        invalidLead = lead.Adapt<V2InvalidData>();
                        invalidLead.Errors = "Invalid ContactNo";
                        invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                        invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                        invalidLead.Created = lead.CreatedOn.Date;
                        invalidLeads.Add(invalidLead);
                        isInvalidContact = true;
                    }
                    lead.ContactNo = contactNo;
                }
                else
                {
                    invalidLead = lead.Adapt<V2InvalidData>();
                    invalidLead.Errors = "Invalid ContactNo";
                    invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                    invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                    invalidLead.Created = lead.CreatedOn.Date;
                    invalidLeads.Add(invalidLead);
                    isInvalidContact = true;
                }
                lead.AlternateContactNo = !string.IsNullOrEmpty(lead.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(lead.AltCountryCode, lead.AlternateContactNo, masterItems.GlobalSettings ?? new()) : null;
                lead.ReferralContactNo = !string.IsNullOrEmpty(lead.AlternateContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, lead.ReferralContactNo, masterItems.GlobalSettings ?? new()) : null;
                lead.ChannelPartnerContactNo = !string.IsNullOrEmpty(lead.ChannelPartnerContactNo) ? phoneUtil.V2ConcatenatePhoneNumber(null, lead.ChannelPartnerContactNo, masterItems.GlobalSettings ?? new()) : null;
                if (string.IsNullOrEmpty(lead.Name))
                {
                    lead.Name = "Unknown";
                }
                if(leadsourceInfo?.Result == null && !isInvalidContact)
                {
                    invalidLead = lead.Adapt<V2InvalidData>();
                    invalidLead.Errors = "Invalid Source";
                    invalidLead.Source = leadSource;
                    invalidLead.SubSource = subSource;
                    invalidLead.Created = lead.CreatedOn.Date;
                    invalidLeads.Add(invalidLead);
                }
                else if (leadsourceInfo?.Result != null)
                {
                    lead.Enquiries.FirstOrDefault().LeadSource = leadsourceInfo.Result.IsValidInfo ? leadsourceInfo.Result.LeadSource : default;
                    lead.Enquiries.FirstOrDefault().SubSource = subSourceInfo.IsValidInfo ? (!string.IsNullOrWhiteSpace(subSourceInfo.SubSource) ? subSourceInfo.SubSource.ToLower() : null) : null;
                }
                if (!propertyInfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.BasePropertyType) ? " \n" + "BaseProperty" + " - " + propertyInfo.BasePropertyType : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.SubPropertyType) ? ", \n" + "SubProperty" + " - " + propertyInfo.SubPropertyType : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidNoOfBHK) ? ", \n" + "NoOfBHK" + " - " + propertyInfo.InvalidNoOfBHK : string.Empty;
                    lead.Notes += !string.IsNullOrEmpty(propertyInfo.InvalidBHKType) ? ", \n" + "BHKType" + " - " + propertyInfo.InvalidBHKType : string.Empty;
                }
                if (!upperBudgetinfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(upperBudgetinfo.Invalidbudget) ? ", \n" + "UpperBudget" + " - " + upperBudgetinfo.Invalidbudget : string.Empty;
                }
                if (!lowerBudgetInfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(lowerBudgetInfo.Invalidbudget) ? ", \n" + "LowerBudget" + " - " + lowerBudgetInfo.Invalidbudget : string.Empty;
                }
                if (leadsourceInfo?.Result != null && !leadsourceInfo.Result.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(leadsourceInfo.Result.InvalidLeadSource) ? ", \n" + "LeadSource" + " - " + leadsourceInfo.Result.InvalidLeadSource : string.Empty;
                }
                if (!enquiryTypesInfo.IsValidInfo)
                {
                    lead.Notes += !string.IsNullOrEmpty(enquiryTypesInfo.InvalidEnquiredFor) ? ", \n" + "EnquiryFOr" + " - " + enquiryTypesInfo.InvalidEnquiredFor : string.Empty;
                }
                if (!(leadStatus?.IsValidStatus(masterItems.LeadStatuses) ?? false))
                {
                    var baseStatusString = mappedColumnsData.GetStringValue(DataColumns.BaseStatus, row);
                    var subStatusString = mappedColumnsData.GetStringValue(DataColumns.SubStatus, row);
                    lead.Notes += !string.IsNullOrWhiteSpace(baseStatusString) ? $"\n{mappedColumnsData[DataColumns.BaseStatus]} - {baseStatusString}" : string.Empty;
                    lead.Notes += !string.IsNullOrWhiteSpace(subStatusString) ? $"\n{mappedColumnsData[DataColumns.SubStatus]} - {subStatusString}" : string.Empty;
                }
                var existingContacts = leads.SelectMany(i => new[] { i.ContactNo, i.AlternateContactNo })
                   .Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                if (invalidLead == null && (!existingContacts.Contains(lead.ContactNo) && !existingContacts.Contains(lead.AlternateContactNo)))
                {
                    leads.Add(lead);
                }
                else if ((existingContacts.Contains(lead.ContactNo) || existingContacts.Contains(lead.AlternateContactNo)))
                {
                    invalidLead = lead.Adapt<V2InvalidData>();
                    invalidLead.Errors = "Duplicate ContactNo";
                    invalidLead.Source = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                    invalidLead.SubSource = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.SubSource ?? lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString();
                    invalidLead.Created = lead.CreatedOn.Date;
                    invalidLeads.Add(invalidLead);
                }
            }
            return (leads, invalidLeads);
        }
        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?$");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }
        public static (double, Guid) GetUnitDetails(string unitAreaSize, List<MasterAreaUnit> areaUnits, string unit, Guid? areaUnitId = null)
        {
            var unitArea = GetArea(unitAreaSize);
            if (areaUnits.Count > 0)
            {
                var masterUnits = areaUnits?.ConvertAll(i => i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)?.ToList();
                var unitOfArea = Regex.Replace(unitAreaSize, "[a-zA-Z]", string.Empty).Trim();
                if (!string.IsNullOrWhiteSpace(unitOfArea) && !string.IsNullOrWhiteSpace(unit) && (masterUnits?.Any(i => i.Contains(unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty)) ?? false))
                {
                    var normalizedUnit = unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty;
                    var unitId = areaUnits?.FirstOrDefault(i => (i?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim() ?? string.Empty).Contains(normalizedUnit))?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
                else if (areaUnitId != null)
                {
                    return (unitArea, areaUnitId ?? Guid.Empty);
                }
                else if (string.IsNullOrWhiteSpace(unit))
                {
                    var unitId = areaUnits?.FirstOrDefault(i => i.Unit == "Sq. Feet")?.Id ?? Guid.Empty;
                    return (unitArea, unitId);
                }
            }
            return (unitArea, Guid.Empty);
        }
        public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;
        }
        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;

        }
        public static string GetCurrencySymbol1(this Dictionary<DataColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
                .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
                    .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
                    .SelectMany(c => c.Currencies)
                    .Select(currency => currency.IsoCode))
                .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }

        }

        public static void SetLeadGoogleSheet(this Domain.Entities.Lead lead,
          Dictionary<DataColumns, string>? mappedColumnsData, Guid currentUserId)
        {
            try
            {
                if (lead != null)
                {
                    if (!string.IsNullOrWhiteSpace(lead.ContactNo))
                    {
                        var multiNumbers = lead.ContactNo.Contains(',') ? lead.ContactNo.Split(',') : lead.ContactNo.Contains('\\') ? lead.ContactNo.Split('\\') : lead.ContactNo.Split('/');
                        if (multiNumbers.Length > 1 && !mappedColumnsData.ContainsKey(DataColumns.AlternateContactNo))
                        {
                            lead.AlternateContactNo = multiNumbers[1];
                        }
                        lead.ContactNo = multiNumbers[0];
                        if (lead.ContactNo.ToLower().Contains('e'))
                        {
                            if (double.TryParse(lead.ContactNo.Replace("+91", ""), out double cNumber))
                            {
                                lead.ContactNo = (cNumber).ToString().Split('.')[0];
                            }
                        }
                        lead.ContactNo = Regex.Replace(lead.ContactNo, @"[^0-9]+", "");
                        if (lead.ContactNo.Length > 1) { lead.ContactNo = lead.ContactNo; }; lead.ContactNo = $"+91{lead.ContactNo.Trim()}";
                    }
                    string name = lead?.Name?.Trim() ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(name))
                    {
                        lead.LeadNumber = (char)(new Random().Next(65, 90)) + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                    }
                    lead.TagInfo = new();
                    lead.CreatedBy = currentUserId;
                    lead.LastModifiedBy = currentUserId;
                    if (lead.Enquiries.Any())
                    {
                        try
                        {
                            lead.Enquiries.FirstOrDefault().LeadSource = LeadSource.GoogleSheet;
                        }
                        catch { }
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public static List<string> GetAgencyNamesFromDataTable(
  DataTable table,
  Dictionary<DataColumns, string> mappedColumnsData)
        {
            var agencyNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(DataColumns.AgencyName, out var agencyColumn) &&
                    !string.IsNullOrEmpty(agencyColumn))
                {
                    string agencyName = row[agencyColumn].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        agencyNames.Add(agencyName);
                    }
                }
            }

            return agencyNames.Distinct().ToList();
        }

        public static List<string> GetCpNamesFromDataTable(
  DataTable table,
  Dictionary<DataColumns, string> mappedColumnsData)
        {
            var cpNames = new HashSet<string>();

            foreach (DataRow row in table.Rows)
            {

                if (mappedColumnsData.TryGetValue(DataColumns.ChannelPartnerName, out var Cpname) &&
                    !string.IsNullOrEmpty(Cpname))
                {
                    string agencyName = row[Cpname].ToString();
                    if (!string.IsNullOrEmpty(agencyName))
                    {
                        cpNames.Add(agencyName);
                    }
                }
            }

            return cpNames.Distinct().ToList();
        }
        public static (string, long) GenerateDisplayIndexPrefixAsync(string prefix, long maxSerial)
        {
            long nextSerial = maxSerial + 1;
            return ($"{prefix}{nextSerial:0000000000}", nextSerial);
        }
    }

    public static class EnumFromDescription
    {
        public static T? GetValueFromDescription<T>(this string description) where T : struct
        {
            if (string.IsNullOrWhiteSpace(description))
            {
                return default;
            }
            var names = Enum.GetNames(typeof(T)).ToList();
            var name = names.FirstOrDefault(i => i.Replace(" ", "").ToLower().Contains(description.Replace(" ", "").Trim().ToLowerInvariant()));
            if (!string.IsNullOrWhiteSpace(name))
            {
                return Enum.Parse<T>(name);
            }

            foreach (var field in typeof(T).GetFields())
            {
                if (Attribute.GetCustomAttribute(field,
                typeof(DescriptionAttribute)) is DescriptionAttribute attribute)
                {
                    if (attribute.Description.Replace(" ", "").Trim().ToLowerInvariant() == description.Replace(" ", "").Trim().ToLowerInvariant()
                       || attribute.Description.Replace(" ", "").Trim().ToLowerInvariant().Contains(description.Replace(" ", "").Trim().ToLowerInvariant()))
                    {
                        return (T)field.GetValue(null);
                    }
                    else if (field.Name.Replace(" ", "").Trim().ToLowerInvariant() == description.Replace(" ", "").Trim().ToLowerInvariant()
                       || field.Name.Replace(" ", "").Trim().ToLowerInvariant().Contains(description.Replace(" ", "").Trim().ToLowerInvariant()))
                    {
                        return (T)field.GetValue(null);
                    }
                    else if (field.Name.Replace(" ", "").Trim().ToLowerInvariant().Contains(description.Replace(" ", "").Trim().ToLowerInvariant()))
                    {
                        return (T)field.GetValue(null);
                    }
                    else if (description.Replace(" ", "").Trim().ToLowerInvariant().Contains(field.Name.Replace(" ", "").Trim().ToLowerInvariant()))
                    {
                        return (T)field.GetValue(null);
                    }
                }
                else
                {
                    if (field.Name == description)
                        return (T)field.GetValue(null);
                }
            }
            return default(T);
        }

    }
}