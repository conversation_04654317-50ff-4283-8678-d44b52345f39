﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Mobile.Dtos.V2;
using Lrb.Application.ZonewiseLocation.Mobile.Helpers;
using Lrb.Application.ZonewiseLocation.Mobile.Requests;
using Lrb.Application.ZonewiseLocation.Mobile.Specs;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Mobile.Requests.V2
{
    public class CreatePropertyListingV2Request : CreatePropertyDtoV2, IRequest<Response<UpdatePropertyDtoV2>>
    {
    }

    public class CreatePropertyListingV2RequestHandler : IRequestHandler<CreatePropertyListingV2Request, Response<UpdatePropertyDtoV2>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepository;
        private readonly IGooglePlacesService _googlePlacesApiService;
        private readonly IRepositoryWithEvents<Domain.Entities.Location> _locationRepo;
        private readonly IMediator _mediator;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        private readonly IRepositoryWithEvents<ListingSourceAddress> _listingAddressRepo;
        private IRepositoryWithEvents<PropertyGallery> _propertyGallleryRepository;
        public CreatePropertyListingV2RequestHandler(
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepository,
            IRepositoryWithEvents<PropertyAssignment> propertyAssignmentRepository,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyTypeRepository,
            IGooglePlacesService googlePlacesApiService,
            IRepositoryWithEvents<Domain.Entities.Location> locationRepo,
            IMediator mediator,
            IRepositoryWithEvents<Domain.Entities.Project> projectsRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo,
            IRepositoryWithEvents<ListingSourceAddress> listingAddressRepo,
            IRepositoryWithEvents<PropertyOwnerDetails> propertyOwnerDetailsRepo,
            IRepositoryWithEvents<PropertyGallery> propertyGallleryRepository
            )
        {
            _propertyRepository = propertyRepository;
            _addressRepo = addressRepo;
            _masterPropertyTypeRepository = masterPropertyTypeRepository;
            _googlePlacesApiService = googlePlacesApiService;
            _locationRepo = locationRepo;
            _mediator = mediator;
            _projectsRepo = projectsRepo;
            _userService = userService;
            _globalsettingRepo = globalsettingRepo;
            _listingAddressRepo = listingAddressRepo;
            _propertyGallleryRepository = propertyGallleryRepository;
        }
        public async Task<Response<UpdatePropertyDtoV2>> Handle(CreatePropertyListingV2Request request, CancellationToken cancellationToken)
        {
            MasterPropertyType? propertyType = null;
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);


            #region Master Property Type
            if (request.PropertyTypeId != Guid.Empty && request.PropertyTypeId != default)
            {
                propertyType = await _masterPropertyTypeRepository.GetByIdAsync(request.PropertyTypeId ?? Guid.Empty);
                if (propertyType == null)
                {
                    throw new InvalidOperationException("Property type id does not belong to Master data.");
                }
            }
            #endregion

            #region Address
            Address? address = null;
            if (request.Address?.LocationId != null && request.Address?.LocationId != Guid.Empty)
            {
                address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { request.Address?.LocationId ?? Guid.Empty }), cancellationToken);
                if (address == null)
                {
                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(request.Address?.LocationId ?? Guid.Empty), cancellationToken);
                    if (location != null)
                    {
                        address = location.MapToAddress();
                        if (address != null)
                        {
                            address.Id = Guid.Empty;
                            address = await _addressRepo.AddAsync(address);
                        }
                    }
                }
            }
            else if ((!string.IsNullOrWhiteSpace(request.PlaceId) || !string.IsNullOrWhiteSpace(request.Address?.PlaceId)) && address == null)
            {
                var placeId = request.Address?.PlaceId ?? request.PlaceId ?? string.Empty;
                address = (await _addressRepo.ListAsync(new AddressByPlaceIdSpec(placeId), cancellationToken))?.FirstOrDefault();
                if (address == null)
                {
                    address = (await _googlePlacesApiService.GetPlaceDetailsByPlaceIdAsync(placeId))?.Adapt<Address>();
                    address = await _addressRepo.AddAsync(address ?? new());
                    await MapAddressToLocationAndSaveAsync(address);
                }
            }
            else if (address == null && (request.Address?.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
            {
                if (newAddress != null)
                {
                    var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                    if (existingAddress == null)
                    {
                        address = await _addressRepo.AddAsync(newAddress);
                    }
                    else
                    {
                        address = existingAddress;
                    }
                    await MapAddressToLocationAndSaveAsync(address);
                }
            }
            #endregion

            List<PropertyAssignment> propertyAssignments = new();
            if (request.AssignedTo?.Any() ?? false)
            {
                propertyAssignments = await SetPropertyAssignmentToAsync(request, cancellationToken);
            }
            var property = request.Adapt<Domain.Entities.Property>();
            property.PropertyAssignments = propertyAssignments;
            property.ListingStatus = ListingStatus.Draft;
            property.Address = address;
            property.PropertyType = propertyType;
            property.OwnerDetails ??= new();
            property.Dimension ??= new();
            property.MonetaryInfo ??= new();
            property.MonetaryInfo.Currency = request?.MonetaryInfo?.Currency ?? countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
            property.Dimension.Currency = request?.Dimension?.Currency ?? request?.MonetaryInfo?.Currency ?? countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
                if (!string.IsNullOrWhiteSpace(request.Project))
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectsRepo.ListAsync(new GetProjectsByNameSpecsV2(request.Project), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        projects.Add(existingProject);
                    }
                    else
                    {
                        Lrb.Domain.Entities.Project tempProjects = new()
                        {
                            Name = request.Project,
                            MonetaryInfo = new ProjectMonetaryInfo
                            {
                                Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        tempProjects = await _projectsRepo.AddAsync(tempProjects, cancellationToken);
                        projects.Add(tempProjects);
                    }
                    property.Project = projects.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            if (globalSettings != null && globalSettings.ShouldEnablePropertyListing && (request?.ListingAddresses?.Any() ?? false))
            {
                List<ListingSourceAddress> sourceAddresses = new List<ListingSourceAddress>();
                foreach (var listingAddress in request.ListingAddresses)
                {
                    var sourceAddress = await _listingAddressRepo.FirstOrDefaultAsync(new GetListedAddressFromLrbTable(listingAddress?.TowerName ?? string.Empty, listingAddress?.SubCommunity ?? string.Empty, listingAddress?.Community ?? string.Empty, listingAddress?.City ?? string.Empty, listingAddress?.SourceId ?? Guid.Empty));
                    if (sourceAddress != null)
                    {
                        sourceAddresses.Add(sourceAddress);
                    }
                    else
                    {
                        var newSourceAddress = request.ListingAddresses.Adapt<ListingSourceAddress>();
                        sourceAddresses.Add(await _listingAddressRepo.AddAsync(newSourceAddress, cancellationToken));
                    }
                }
                property.ListingSourceAddresses = sourceAddresses;
            }
            property.OwnerDetails = null;
            property = await _propertyRepository.AddAsync(property);

            var groupedImageUrls = request.ImageUrls;
            if (groupedImageUrls != null && groupedImageUrls.Any())
            {
                foreach (var group in groupedImageUrls.Where(i => i.Value.Any(i => !string.IsNullOrWhiteSpace(i.ImageFilePath))))
                {
                    List<PropertyGalleryDtoV2> imageUrls = group.Value;
                    if (imageUrls != null && imageUrls.Any())
                    {
                        foreach (var image in imageUrls)
                        {
                            if (!string.IsNullOrEmpty(image.ImageFilePath))
                            {
                                await _propertyGallleryRepository.AddAsync(new PropertyGallery()
                                {
                                    PropertyId = property.Id,
                                    ImageKey = string.IsNullOrWhiteSpace(group.Key) ? "default" : group.Key,
                                    ImageFilePath = image.ImageFilePath,
                                    IsCoverImage = image.IsCoverImage,
                                    Height = image.Height,
                                    Width = image.Width
                                });
                            }
                        }
                    }
                }
            }
            var images = request.Images;
            if (images != null && images.Any())
            {
                var filteredImageUrls = images.Where(dto => !string.IsNullOrWhiteSpace(dto.ImageFilePath)).ToList();

                if (filteredImageUrls.Any())
                {
                    var propertyGallery = filteredImageUrls.Select(dto => new PropertyGallery
                    {
                        Id = Guid.NewGuid(),
                        Name = dto.Name,
                        ImageFilePath = dto.ImageFilePath,
                        PropertyId = property.Id,
                        GalleryType = dto.GalleryType,
                        IsCoverImage = dto.IsCoverImage,
                        ImageKey = dto.ImageKey,
                        OrderRank = dto.OrderRank,
                        Height = dto.Height,
                        Width = dto.Width

                    }).ToList();
                    if (propertyGallery.Any())
                    {
                        await _propertyGallleryRepository.AddRangeAsync(propertyGallery);
                    }
                }
            }


            return new(property.Adapt<UpdatePropertyDtoV2>());
        }

        #region Address Mapping
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
        #endregion

        #region Assignment
        private async Task<List<PropertyAssignment>> SetPropertyAssignmentToAsync(CreatePropertyListingV2Request request, CancellationToken cancellationToken)
        {
            List<PropertyAssignment> propertyAssignments = new();
            foreach (Guid id in request?.AssignedTo ?? new())
            {
                var userName = await _userService.GetAsync(id.ToString() ?? string.Empty, cancellationToken);
                PropertyAssignment propertyAssigned = new();
                propertyAssigned.AssignedTo = id;
                propertyAssigned.AssignedUser = userName.FirstName + " " + userName.LastName;
                propertyAssigned.IsCurrentlyAssigned = true;
                propertyAssignments.Add(propertyAssigned);
            }
            return propertyAssignments;
        }
        #endregion
    }
}
