﻿using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Mobile;
using Lrb.Application.ZonewiseLocation.Web.Helpers;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web
{
    public class CreatePropertyRequest : CreatePropertyDto, IRequest<Response<UpdatePropertyDto>>
    {
        public virtual Guid CreatedBy { get; set; }
        public virtual Guid LastModifiedBy { get; set; }
        public DateTime CreatedOn { get; private set; }
        public DateTime? LastModifiedOn { get; set; }
    }
    public class CreatePropertyRequestHandler : IRequestHandler<CreatePropertyRequest, Response<UpdatePropertyDto>>
    {

        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.PropertyAssignment> _propertyAssignmentRepository;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepository;
        private readonly IGooglePlacesService _googlePlacesApiService;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IMediator _mediator;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        private readonly IRepositoryWithEvents<ListingSourceAddress> _listingAddressRepo;
        private readonly IRepositoryWithEvents<PropertyOwnerDetails> _propertyOwnerDetailsRepo;

        public CreatePropertyRequestHandler(

            IRepositoryWithEvents<Domain.Entities.Property> propertyRepository,
            IRepositoryWithEvents<Domain.Entities.PropertyAssignment> propertyAssignmentRepository,
            IRepositoryWithEvents<Address> propertyAddressRepository,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyTypeRepository,
            IRepositoryWithEvents<Location> locationRepository,
            IGooglePlacesService googlePlacesApiService,
            IMediator mediator,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo,
            IRepositoryWithEvents<ListingSourceAddress> listingAddressRepo,
            IRepositoryWithEvents<PropertyOwnerDetails> propertyOwnerDetailsRepo)

        {

            _propertyRepository = propertyRepository;
            _propertyAssignmentRepository = propertyAssignmentRepository;
            _addressRepo = propertyAddressRepository;
            _masterPropertyTypeRepository = masterPropertyTypeRepository;
            _googlePlacesApiService = googlePlacesApiService;
            _locationRepo = locationRepository;
            _mediator = mediator;
            _projectsRepo = projectsRepo;
            _userService = userService;
            _globalsettingRepo = globalsettingRepo;
            _listingAddressRepo = listingAddressRepo;
            _propertyOwnerDetailsRepo = propertyOwnerDetailsRepo;
        }
        public async Task<Response<UpdatePropertyDto>> Handle(CreatePropertyRequest request, CancellationToken cancellationToken)
        {
            MasterPropertyType? propertyType = null;
            Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);

            if (request.PropertyTypeId != Guid.Empty && request.PropertyTypeId != default)
            {
                propertyType = await _masterPropertyTypeRepository.GetByIdAsync(request.PropertyTypeId ?? Guid.Empty);
                if (propertyType == null)
                {
                    throw new InvalidOperationException("Property type id does not belong to Master data.");
                }
            }
            Address? address = null;
            if (request.Address?.LocationId != null && request.Address?.LocationId != Guid.Empty)
            {
                address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { request.Address?.LocationId ?? Guid.Empty }), cancellationToken);
                if (address == null)
                {
                    var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(request.Address?.LocationId ?? Guid.Empty), cancellationToken);
                    if (location != null)
                    {
                        address = location.MapToAddress();
                        if (address != null)
                        {
                            address.Id = Guid.Empty;
                            address = await _addressRepo.AddAsync(address);
                        }
                    }
                }
            }
            else if ((!string.IsNullOrWhiteSpace(request.PlaceId) || !string.IsNullOrWhiteSpace(request.Address?.PlaceId)) && address == null)
            {
                var placeId = request.Address?.PlaceId ?? request.PlaceId ?? string.Empty;
                address = (await _addressRepo.ListAsync(new AddressByPlaceIdSpec(placeId), cancellationToken))?.FirstOrDefault();
                if (address == null)
                {
                    address = (await _googlePlacesApiService.GetPlaceDetailsByPlaceIdAsync(placeId))?.Adapt<Address>();
                    address = await _addressRepo.AddAsync(address ?? new());
                    await MapAddressToLocationAndSaveAsync(address);
                }
            }
            //else if (address == null)
            //{
            //    if (request.Address != null)
            //    {
            //        address = await _addressRepo.AddAsync(request.Address?.Adapt<Address>() ?? new());
            //        await MapAddressToLocationAndSaveAsync(address);
            //    }
            //}
            else if (address == null && (request.Address?.Adapt<Address>()?.Validate(out Address? newAddress) ?? false))
            {
                if (newAddress != null)
                {
                    var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                    if (existingAddress == null)
                    {
                        address = await _addressRepo.AddAsync(newAddress);
                    }
                    else
                    {
                        address = existingAddress;
                    }
                    await MapAddressToLocationAndSaveAsync(address);
                }
            }
            List<PropertyAssignment> propertyAssignments = new();
            if (request.AssignedTo?.Any() ?? false)
            {
                propertyAssignments = await SetPropertyAssignmentToAsync(request, cancellationToken);
            }
            PropertyTagInfo propertyTags = request.TagInfo?.Adapt<PropertyTagInfo>() ?? new();
            propertyTags.IsValidated = request.IsValid();
            var property = request.Adapt<Domain.Entities.Property>();
            property.TaxationMode = request.TaxationMode;
            property.PropertyAssignments = propertyAssignments;
            if(globalSettings?.ShouldEnablePropertyListing == true)
            {
                property.ListingStatus = ListingStatus.Draft;
            }
            property.ListingStatus = ListingStatus.Draft;
            property.Address = address;
            property.PropertyType = propertyType;
            property.TagInfo = propertyTags;
            property.OwnerDetails ??= new();
            property.TenantContactInfo ??= new();
            property.Dimension ??= new();
            property.MonetaryInfo ??= new();
            property.MonetaryInfo.Currency = request?.MonetaryInfo?.Currency ??countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
            property.Dimension.Currency = request?.Dimension?.Currency ?? request?.MonetaryInfo?.Currency ?? countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";

            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
                if (!string.IsNullOrWhiteSpace(request.Project))
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectsRepo.ListAsync(new GetProjectsByNameSpecsV2(request.Project), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        projects.Add(existingProject);
                    }
                    else
                    {
                        Lrb.Domain.Entities.Project tempProjects = new()
                        {
                            Name = request.Project,
                            MonetaryInfo = new ProjectMonetaryInfo
                            {
                                Currency =countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        tempProjects = await _projectsRepo.AddAsync(tempProjects, cancellationToken);
                        projects.Add(tempProjects);
                    }
                    //property.Projects = projects;
                    property.Project = projects.FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            if(request?.AdditionalProperties?.Any() ?? false)
            {
                property.AdditionalProperties = request.AdditionalProperties;
            }
            if(globalSettings != null && globalSettings.ShouldEnablePropertyListing && (request?.ListingAddresses?.Any() ?? false))
            {
                List<ListingSourceAddress> sourceAddresses = new List<ListingSourceAddress>();
                foreach(var listingAddress in request.ListingAddresses)
                {
                    var sourceAddress = await _listingAddressRepo.FirstOrDefaultAsync(new GetListingSourceAddressByIdSpecs(listingAddress?.LocationId ?? Guid.Empty));
                    if (sourceAddress != null)
                    {
                        sourceAddresses.Add(sourceAddress);
                    }
                }
                property.ListingSourceAddresses = sourceAddresses;
            }
            property.OwnerDetails = null;
            property = await _propertyRepository.AddAsync(property);
            return new Response<UpdatePropertyDto>(property.Adapt<UpdatePropertyDto>());
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            var location = address.MapToLocationRequest();
            if (location != null)
            {
                var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                address.Location = createdLocation;
                await _addressRepo.UpdateAsync(address);
            }
        }
        private async Task<List<PropertyAssignment>> SetPropertyAssignmentToAsync(CreatePropertyRequest request, CancellationToken cancellationToken)
        {
            List<PropertyAssignment> propertyAssignments = new();
            foreach (Guid id in request.AssignedTo)
            {
                var userName = await _userService.GetAsync(id.ToString() ?? string.Empty, cancellationToken);
                PropertyAssignment propertyAssigned = new();
                propertyAssigned.AssignedTo = id;
                propertyAssigned.AssignedUser = userName.FirstName + " " + userName.LastName;
                propertyAssigned.IsCurrentlyAssigned = true;
                propertyAssignments.Add(propertyAssigned);

            }

            return propertyAssignments;
        }
    }
}
