﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.Lead.Utils;
using Lrb.Domain.Entities.CustomAddress;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using PhoneNumbers;
using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public static class BulkProjectUploadHelper
    {
        public static List<string> GetUnmappedColumnNames(this DataTable table, Dictionary<ProjectDataColumns, string> mappedColumnsData)
        {
            List<string> columns = new();
            if (mappedColumnsData.ContainsKey(ProjectDataColumns.Notes))
            {
                if (!string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Notes]))
                {
                    columns.Add(mappedColumnsData[ProjectDataColumns.Notes]);
                    mappedColumnsData.Remove(ProjectDataColumns.Notes);
                }

            }
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }
        public static List<Domain.Entities.Project> ConvertToProject(
            this DataTable table,
            Dictionary<ProjectDataColumns, string>? mappedColumnsData,
            List<string> unMappedColumns,
            List<MasterProjectType> projectTypes,
            List<MasterAreaUnit> areaUnits,
            List<MasterAssociatedBank> associatedBanks,
            List<CustomMasterAmenity> projectAmenities,
            Domain.Entities.GlobalSettings globalSettings, string jsonData = null,
             IBlobStorageService? _blobStorageService = null
            )
           {
            List<Domain.Entities.Project> projects = new List<Domain.Entities.Project>();
            Guid? defaultUnitId = Guid.TryParse(globalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;
            string? defaultUnit = areaUnits.FirstOrDefault(i => i.Id == defaultUnitId)?.Unit ?? "Sq. Feet";
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);

            foreach (DataRow row in table.Rows)
            {
                #region GetData
                string? projectType = !mappedColumnsData.ContainsKey(ProjectDataColumns.ProjectType) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.ProjectType]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.ProjectType]].ToString();
                string? projectsubType = !mappedColumnsData.ContainsKey(ProjectDataColumns.ProjectSubType) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.ProjectSubType]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.ProjectSubType]].ToString();
                string? projectName = !mappedColumnsData.ContainsKey(ProjectDataColumns.Name) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Name]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.Name]].ToString();
                string? certificate = !mappedColumnsData.ContainsKey(ProjectDataColumns.Certificate) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Certificate]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.Certificate]].ToString();
                string? status = !mappedColumnsData.ContainsKey(ProjectDataColumns.ProjectStatus) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.ProjectStatus]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.ProjectStatus]].ToString();
                string? brokerageAmount = !mappedColumnsData.ContainsKey(ProjectDataColumns.BrokerageAmount) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.BrokerageAmount]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.BrokerageAmount]].ToString();
                string? possessionType = !mappedColumnsData.ContainsKey(ProjectDataColumns.PossesionType) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.PossesionType]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.PossesionType]].ToString();
                string? possessionDate = !mappedColumnsData.ContainsKey(ProjectDataColumns.PossessionDate) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.PossessionDate]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.PossessionDate]].ToString();
                string? facing = !mappedColumnsData.ContainsKey(ProjectDataColumns.Facing) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Facing]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.Facing]].ToString();
                string? discription = !mappedColumnsData.ContainsKey(ProjectDataColumns.Discription) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Discription]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.Discription]].ToString();
                string? city = !mappedColumnsData.ContainsKey(ProjectDataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.City]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.City]].ToString();
                string? state = !mappedColumnsData.ContainsKey(ProjectDataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.State]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.State]].ToString();
                string? location = !mappedColumnsData.ContainsKey(ProjectDataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Location]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.Location]].ToString();
                string? builderName = !mappedColumnsData.ContainsKey(ProjectDataColumns.BuilderName) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.BuilderName]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.BuilderName]].ToString();
                string? builderPhoneNumber = !mappedColumnsData.ContainsKey(ProjectDataColumns.BuilderContactNumber) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.BuilderContactNumber]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.BuilderContactNumber]].ToString();
                string? pointofContact = !mappedColumnsData.ContainsKey(ProjectDataColumns.PointofContact) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.PointofContact]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.PointofContact]].ToString();
                string pointofContactcountrycode = !mappedColumnsData.ContainsKey(ProjectDataColumns.pointofContactcountrycode) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.pointofContactcountrycode]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.pointofContactcountrycode]].ToString();
                var totalBlocks = !mappedColumnsData.ContainsKey(ProjectDataColumns.TotalBlocks) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.TotalBlocks]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.TotalBlocks]].ToString(); 
                string? totalFloors = !mappedColumnsData.ContainsKey(ProjectDataColumns.TotalFloors) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.TotalFloors]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.TotalFloors]].ToString();
                string? totalUnits = !mappedColumnsData.ContainsKey(ProjectDataColumns.TotalUnits) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.TotalUnits]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.TotalUnits]].ToString();
                string? minPrice = !mappedColumnsData.ContainsKey(ProjectDataColumns.MinPrice) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.MinPrice]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.MinPrice]].ToString();
                string? maxPrice = !mappedColumnsData.ContainsKey(ProjectDataColumns.MaxPrice) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.MaxPrice]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.MaxPrice]].ToString();
                string? projectPrice = !mappedColumnsData.ContainsKey(ProjectDataColumns.ProjectPrice) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.ProjectPrice]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.ProjectPrice]].ToString();
                string? reranumber = !mappedColumnsData.ContainsKey(ProjectDataColumns.RERANumber) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.RERANumber]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.RERANumber]].ToString();
                string? projectArea = !mappedColumnsData.ContainsKey(ProjectDataColumns.LandArea) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.LandArea]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.LandArea]].ToString();
                string? unit = !mappedColumnsData.ContainsKey(ProjectDataColumns.AreaUnit) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.AreaUnit]) ? defaultUnit : row[mappedColumnsData[ProjectDataColumns.AreaUnit]].ToString();
                string currecncy = !mappedColumnsData.ContainsKey(ProjectDataColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Currency]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.Currency]].ToString();
                string countrycode = !mappedColumnsData.ContainsKey(ProjectDataColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.CountryCode]].ToString();
                string BrokerageCurrency = !mappedColumnsData.ContainsKey(ProjectDataColumns.BrokerageCurrency) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.BrokerageCurrency]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.BrokerageCurrency]].ToString();
                #endregion
                var propjectInfo = GetProjectType(projectType, projectsubType, projectTypes);
                var brokerageInfo = GetBrokerageAmount(brokerageAmount ?? string.Empty);
                var isValidFacingInfo = Enum.TryParse<Facing>(facing, true, out var facingInfo);
                var currency = mappedColumnsData.GetCurrencySymbol1(row, currecncy, countries?.FirstOrDefault().DefaultCurrency);
                var brokeragecurrency = mappedColumnsData.GetBrokerageCurrencySymbol(row, BrokerageCurrency, countries?.FirstOrDefault().DefaultCurrency);
                List<string> reraNumbers = string.IsNullOrEmpty(reranumber) ? new List<string>() : reranumber.Split(',').Select(x => x.Trim()).ToList();
                var validBuilderPhone = mappedColumnsData.ValidateContactNo(row, builderPhoneNumber, countrycode,countries?.FirstOrDefault().DefaultCallingCode);
                var validPOCPhoneNo = mappedColumnsData.ValidateContactNo(row, pointofContact, pointofContactcountrycode, countries?.FirstOrDefault().DefaultCallingCode);
                var areaUnitId = areaUnits.FirstOrDefault(x => string.Equals(x.Unit?.ToLower().Replace(" ", ""), unit?.ToLower().Replace(" ", ""), StringComparison.OrdinalIgnoreCase))?.Id ?? areaUnits.FirstOrDefault(x => x.Unit == defaultUnit)?.Id;
                var banks = !mappedColumnsData.ContainsKey(ProjectDataColumns.AssociatedBanks) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.AssociatedBanks]) ? null : row[mappedColumnsData[ProjectDataColumns.AssociatedBanks]].ToString();
                var associatebank = banks?.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
                var associatedBankslist = associatedBanks?.Where(i => associatebank != null && associatebank.Any(name => i.Name.Trim().ToLower().Equals(name.Trim().ToLower(), StringComparison.InvariantCultureIgnoreCase))).Select(i => i.Id).ToList();
                var amenities = !mappedColumnsData.ContainsKey(ProjectDataColumns.Amenities) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.Amenities]) ? null : row[mappedColumnsData[ProjectDataColumns.Amenities]].ToString();
                var associateamenity = amenities?.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrWhiteSpace(p)).ToList();
                var amenitieslist = projectAmenities?.Where(i => associateamenity != null && associateamenity.Any(name => i.AmenityDisplayName.Trim().ToLower().Replace(" ","").Equals(name.Trim().ToLower().Replace(" ", ""), StringComparison.InvariantCultureIgnoreCase))).Select(i => i.Id).ToList();
                string? imagesUrls = !mappedColumnsData.ContainsKey(ProjectDataColumns.ImageUrls) || string.IsNullOrEmpty(mappedColumnsData[ProjectDataColumns.ImageUrls]) ? string.Empty : row[mappedColumnsData[ProjectDataColumns.ImageUrls]].ToString();
                if (double.TryParse(possessionDate, out var oADate))
                {
                    possessionDate = (DateTime.FromOADate(oADate)).ToString("dd/MM/yyyy");
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(jsonData))
                    {
                        CommonTimeZoneDto commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                        possessionDate = (DateTime.Parse(possessionDate).ToParticularTimeZone(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset)).ToString("dd/MM/yyyy HH:mm:ss");
                    }
                }
                var isValidPossessionDate = DateTime.TryParse(possessionDate, new CultureInfo("de-IN"), DateTimeStyles.NoCurrentDateDefault, out DateTime dateTime);
                if (isValidPossessionDate)
                {
                    possessionDate = dateTime.ToUniversalTime().ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                }
                else
                {
                    possessionDate = null;
                }
                List<string> images = new();
                if (!string.IsNullOrEmpty(imagesUrls))
                {
                    images = imagesUrls.Split(new[] { '|', ',' }, StringSplitOptions.RemoveEmptyEntries).Select(i => i.Trim()).ToList();
                }
                var project = new Domain.Entities.Project()
                {
                    Name = projectName,
                    Certificates = certificate,
                    Facing = isValidFacingInfo ? facingInfo : default,
                    //PossesionType = Enum.TryParse(possessionType, true, out PossesionType parsedtype) ? parsedtype : default,
                    PossesionType = Enum.TryParse(possessionType, true, out PossesionType parsedType) && (parsedType != PossesionType.CustomDate || isValidPossessionDate) ? parsedType : default,
                    PossessionDate = (isValidPossessionDate && Enum.TryParse<PossesionType>(possessionType, true, out var possessionEnum) && possessionEnum == PossesionType.CustomDate) ? dateTime.ToUniversalTime() : null,
                    ProjectType = propjectInfo.IsValidInfo ? propjectInfo.ProjectType : default,
                    AreaUnitId = areaUnitId ?? defaultUnitId,
                    ReraNumber = reraNumbers,
                    TotalBlocks = double.TryParse(totalBlocks, out var blocks) && blocks >= 0 ? blocks : default,
                    TotalFlats = double.TryParse(totalUnits, out var units) && units >= 0 ? units : default,
                    TotalFloor = double.TryParse(totalFloors, out var floors) && floors >= 0 ? floors : default,
                    Area = double.TryParse(projectArea, out var area) && area >= 0 ? area : default,
                    MinimumPrice = double.TryParse(minPrice, out var min) && min >= 0 ? min : null,
                    MaximumPrice = double.TryParse(maxPrice, out var max) && max >= 0 ? max : null,
                    Description = discription,
                    Status = Enum.TryParse(status, true, out ProjectStatus parsedStatus) ? parsedStatus : default,
                    Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                    Address = (!string.IsNullOrWhiteSpace(city) || !string.IsNullOrWhiteSpace(state) || !string.IsNullOrWhiteSpace(location)) ?
                    new()
                    {
                        City = city,
                        State = state,
                        SubLocality = location
                    } :null,
                    BuilderDetail = (!string.IsNullOrWhiteSpace(builderName) || !string.IsNullOrWhiteSpace(validBuilderPhone) || !string.IsNullOrWhiteSpace(validPOCPhoneNo)) ?
                    new()
                    {
                        Name = builderName,
                        ContactNo = validBuilderPhone,
                        PointOfContact = validPOCPhoneNo
                    } :  null,
                    MonetaryInfo = new()
                    {
                        Brokerage = brokerageInfo,
                        Currency = currency,
                        BrokerageCurrency = brokeragecurrency
                    },
                    Facings = GetFacings(facing),
                };
                if (associatedBankslist?.Any() ?? false)
                {
                    project.AssociatedBanks = associatedBankslist?.Select(bankId => new AssociatedBank
                    {
                        MasterAssociatedBankId = bankId
                    }).ToList();
                }
                if (amenitieslist?.Any() ?? false)
                {
                    project.Amenities = amenitieslist?.Select(amenityId => new ProjectAmenity
                    {
                        MasterProjectAmenityId = amenityId
                    }).ToList();
                }
                if ((images?.Any() ?? false) && _blobStorageService != null)
                {
                    var keys = UploadImageInS3(images, _blobStorageService);
                    List<ProjectGallery> galleries = new();
                    foreach (var key in keys)
                    {
                        try
                        {
                            ProjectGallery propertyGallery = new()
                            {
                                ImageKey = key,
                                ImageFilePath = key,
                                ProjectId = project.Id,
                                IsCoverImage = key == keys.FirstOrDefault() ? true : false,
                                Name = $"{GenerateRandomFileName()}",
                                GalleryType = ProjectGalleryType.Image,
                            };
                            galleries.Add(propertyGallery);
                        }
                        catch (Exception ex)
                        {
                            continue;
                        }
                    }
                    project.ProjectGalleries = galleries;
                }

                projects.Add(project);
            }
            return projects;
        }
        #region Upload Property Image In S3
        public static List<string> UploadImageInS3(List<string> imageUrl, IBlobStorageService _blobStorageService)
        {
            List<string> keys = new List<string>();
            try
            {
                foreach (var url in imageUrl)
                {
                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    var response = client.SendAsync(request);
                    response.Result.EnsureSuccessStatusCode();
                    var data = response.Result.Content.ReadAsByteArrayAsync();

                    using (var stream = new MemoryStream(data.Result))
                    {
                        var key = _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", "Images/PropertyImages", GenerateRandomFileName(), stream);
                        keys.Add(key.Result);
                    }
                }
                return keys;
            }
            catch (Exception ex)
            {
                return keys;
            }
        }

        public static string GenerateRandomFileName()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");
            Random random = new Random();
            string randomPart = random.Next(100000000, 999999999).ToString() +
                                random.Next(100000000, 999999999).ToString();

            return datePart + randomPart;
        }
        #endregion

        public static List<Facing> GetFacings(string facings)
        {
            List<Facing> facing = new();
            if (!string.IsNullOrEmpty(facings))
            {
                foreach (string item in facings.Split(','))
                {
                    string cleanedItem = RemoveSpecialCharactersAndNumbers(item);

                    if (Enum.TryParse<Facing>(cleanedItem, true, out Facing type))
                    {
                        facing.Add(type);
                    }
                }
            }
            return facing;
        }
        private static string RemoveSpecialCharactersAndNumbers(string input)
        {
            return Regex.Replace(input, @"[^a-zA-Z]", "").ToLower();
        }
        public static ProjectTypeInfo GetProjectType(string basePropertyType, string subPropertyType, List<MasterProjectType> projectTypes)
        {
      
            if (basePropertyType.ToLower().Contains("residential") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "flat";
            }
            if (basePropertyType.ToLower().Contains("commercial") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "Plot";
            }
            if (basePropertyType.ToLower().Contains("agricultural") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "land";
            }
          
            MasterProjectType projectType = null;
            if (string.IsNullOrEmpty(basePropertyType) && !string.IsNullOrWhiteSpace(subPropertyType))
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower().Trim().Replace(" ", "") == subPropertyType.ToLower().Trim().Replace(" ", "") && !i.IsDeleted);
            }
            if (string.IsNullOrEmpty(basePropertyType) && !string.IsNullOrWhiteSpace(subPropertyType))
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower().Trim().Replace(" ", "") == subPropertyType.ToLower().Trim().Replace(" ", "") && !i.IsDeleted);
            }
            if (string.IsNullOrEmpty(basePropertyType) && !string.IsNullOrWhiteSpace(subPropertyType))
            {
                projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") && i.DisplayName.ToLower().Trim().Replace(" ", "") == subPropertyType.ToLower().Trim().Replace(" ", "") && !i.IsDeleted);
            }
            //  List<string> proprtyTypes = new() { "flat", "independent house", "villa", "residential", "shop", "row villa", "land", "office space", "hostel guest house", "plot", "showroom", "godown", "chambers", "farm house", "basement", "guest house", "kiosk", "complete building", "studio", "farmhouse land", "hotel space", "agricultural land", "industrial space", "co working office space" };
            if (!string.IsNullOrEmpty(basePropertyType) && !string.IsNullOrWhiteSpace(subPropertyType))
            {
                if (!string.IsNullOrEmpty(basePropertyType) && basePropertyType?.ToLower() == "commercial")
                {
                    projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower().Trim().Replace(" ", "") == subPropertyType.ToLower().Trim().Replace(" ", "") && !i.IsDeleted);
                }
                if (!string.IsNullOrEmpty(basePropertyType) && basePropertyType?.ToLower() == "residential")
                {
                    projectType = projectTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower().Trim().Replace(" ", "") == subPropertyType.ToLower().Trim().Replace(" ", "") && !i.IsDeleted);
                }
                else
                {
                    projectType = projectTypes.FirstOrDefault(i => i.DisplayName.ToLower().Trim().Replace(" ", "") == subPropertyType.ToLower().Trim().Replace(" ", ""));
                }
            }
                return new ProjectTypeInfo()
            {
                ProjectType = projectType,
                IsValidInfo = true
            };
        }
        public class ProjectTypeInfo
        {
            public MasterProjectType ProjectType { get; set; }
            public bool IsValidInfo { get; set; }
            public string BaseProjectType { get; set; }
            public string SubProjectType { get; set; }
            public List<string>? Floors { get; set; }
        }
        public static double? GetBrokerageAmount(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?"); 
                Match match = regex.Match(number);
                double? value = null;

                if (match.Success)
                {
                    value = double.Parse(match.Value);
                }
                return value;
            }
            catch
            {
                throw;
            }
        }
        public static void SetProject(this Domain.Entities.Project project,
             Dictionary<ProjectDataColumns, string>? mappedColumnsData, Guid currentUserId)
        {
            try
            {
                if (project != null)
                {
                    if (!string.IsNullOrWhiteSpace(project?.BuilderDetail?.ContactNo))
                    {
                        var multiNumbers = project.BuilderDetail.ContactNo.Contains(',') ? project.BuilderDetail.ContactNo.Split(',') : project.BuilderDetail.ContactNo.Contains('\\') ? project.BuilderDetail.ContactNo.Split('\\') : project.BuilderDetail.ContactNo.Split('/');
                    }
                    project.CreatedBy = currentUserId;
                    project.LastModifiedBy = currentUserId;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public static List<InvalidData> GetInvalidProjects(List<Domain.Entities.Project> projects, List<Domain.Entities.Project> existingprojects)
        {
            List<InvalidData> invalidData = new();
            #region Invalid
            List<Domain.Entities.Project>? invalidProjects = null;
            invalidProjects = projects.Where(i => string.IsNullOrEmpty(i.Name)).ToList();
            if (invalidProjects.Any())
            {
                var ivalidPosseionDate = invalidProjects.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid Project Nane");
                projects.RemoveAll(i => string.IsNullOrEmpty(i.Name));
                invalidData.AddRange(ivalidPosseionDate);
                invalidProjects = null;
            }
            invalidProjects = projects.Where(i => i?.ProjectType == null).ToList();
            if (invalidProjects.Any())
            {
                var ivalidPosseionDate = invalidProjects.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid projectType");
                projects.RemoveAll(i => i?.ProjectType == null);
                invalidData.AddRange(ivalidPosseionDate);
                invalidProjects = null;
            }
            invalidProjects = projects.Where(i => i?.ProjectType == null).ToList();
            if (invalidProjects.Any())
            {
                var ivalidPosseionDate = invalidProjects.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid projectType");
                projects.RemoveAll(i => i?.ProjectType == null);
                invalidData.AddRange(ivalidPosseionDate);
                invalidProjects = null;
            }
            var existingPro = existingprojects.Select(i => i.Name).ToList();
            foreach (var project in projects)
            {
                if (!string.IsNullOrWhiteSpace(project.Name) && existingPro.Contains(project.Name))
                {
                    var invalidProject = project.Adapt<InvalidData>();
                    invalidProject.Errors = "Duplicate Project";

                    var duplicateProject = existingprojects.FirstOrDefault(i => !string.IsNullOrWhiteSpace(i.Name) && i.Name.Equals(project.Name));

                    if (duplicateProject != null)
                    {
                        invalidData.Add(invalidProject);
                    }
                }
                invalidProjects = null;
            }

            #endregion
            return invalidData;
        }
        public static string GetCurrencySymbol1(this Dictionary<ProjectDataColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return defaultcurrency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            var currencies = ISO3166.Country.List
            .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
                .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
                .SelectMany(c => c.Currencies)
                .Select(currency => currency.IsoCode))
            .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);
            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }

        }
        public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
        {
            isoCurrencyCode = CultureInfo
        .GetCultures(CultureTypes.AllCultures)
        .Where(c => !c.IsNeutralCulture)
        .Select(culture =>
        {
            try
            {
                return new RegionInfo(culture.Name);
            }
            catch
            {
                return null;
            }
        })
        .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
        .Select(ri => ri.ISOCurrencySymbol)
        .FirstOrDefault();

            return isoCurrencyCode != null;
        }
        public static List<string> GetValidCurrencySymbols()
        {
            var symbols = new List<string>();
            var countries = ISO3166.Country.List;

            var currencyProvider = new Nager.Country.CountryProvider();

            foreach (var country in countries)
            {
                var matchingCountry = currencyProvider.GetCountries()
                    .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                if (matchingCountry != null)
                {
                    foreach (var currency in matchingCountry.Currencies)
                    {
                        var symbol = currency.IsoCode;
                        symbols.Add(symbol);
                    }
                }
            }

            return symbols;

        }
        public static string GetBrokerageCurrencySymbol(this Dictionary<ProjectDataColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrEmpty(currency))
            {
                return defaultcurrency;
            }
            if (currency == "%")
            {
                return currency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            bool isCurrencyCode = currency.Length != 3;

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }
        }
        public static Lrb.Domain.Entities.Project? GetProject(string newProject, Lrb.Domain.Entities.GlobalSettings globalSettings, List<Lrb.Domain.Entities.Project> projects)
        {
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);

            var project = string.IsNullOrWhiteSpace(newProject) ? null :
                   projects.FirstOrDefault(i => i.Name?.ToLower().Trim() == newProject.ToLower().Trim())
                               ?? new Lrb.Domain.Entities.Project()
                               {
                                   Name = newProject,
                                   MonetaryInfo = new ProjectMonetaryInfo
                                   {
                                       Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                   }
                               };
            return project;
        }
        public static string ValidateContactNo(this Dictionary<ProjectDataColumns, string> mappedColumnsData, DataRow row, string? contactNo, string? countryCode, string? callingCode)
        {

            if (string.IsNullOrEmpty(contactNo))
            {
                return null;
            }

            if (countryCode == string.Empty)
            {
                countryCode = callingCode;
            }
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

            string defaultRegion = null;
            if (!string.IsNullOrEmpty(countryCode))
            {
                int CountryCode = Convert.ToInt32(countryCode);

                var countryToRegionMap = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap();
                if (countryToRegionMap.TryGetValue(CountryCode, out var regionCodes))
                {
                    defaultRegion = regionCodes.FirstOrDefault();
                }
            }
            PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);
            PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);
            string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);
            string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);
            string numericMobileNumber = Regex.Replace(contactWithCountryCode, @"\D", "");
            bool isValid = numericMobileNumber.Length == contactWithCountryCode.Length - 1;
            if (isValid)
            {
                return contactWithCountryCode;
            }
            else
            {
                return null;
            }
        }
    }
}
