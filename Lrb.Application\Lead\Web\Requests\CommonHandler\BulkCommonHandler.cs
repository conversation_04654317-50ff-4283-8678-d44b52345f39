﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Agency.Web;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.NotificationService;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Common.SMS;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.CommonHandler
{
    public class BulkCommonHandler
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly IUserService _userService;
        protected readonly ICurrentUser _currentUserService;
        private readonly string _className;
        private readonly string _methodName;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        protected readonly INotificationSenderService _notificationSenderService;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customStatusRepo;
        protected readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Agency> _agencyRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.QRFormTemplate> _qrFormTemplateRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.ChannelPartner> _cpRepository;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Domain.Entities.Campaign> _campaignRepository;
        public BulkCommonHandler
        (
            IServiceProvider serviceProvider,
            string className,
            string methodName
        )
        {
            _serviceProvider = serviceProvider;
            _className = className;
            _methodName = methodName;
            _leadRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Lead>>();
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _leadRepositoryAsync = _serviceProvider.GetRequiredService<ILeadRepositoryAsync>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Project>>();
            _leadHistoryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadHistory>>();
            _customStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterLeadStatus>>();
            _leadAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAssignment>>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _currentUserService = _serviceProvider.GetRequiredService<ICurrentUser>();
            _propertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
            _propertyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _agencyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Agency>>();
            _qrFormTemplateRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.QRFormTemplate>>();
            _cpRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.ChannelPartner>>();
            _globalsettingRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _mediator = _serviceProvider.GetRequiredService<IMediator>();
            _campaignRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Campaign>>();

        }

        protected async Task<List<Domain.Entities.Project>> GetAllProjectsAsync(List<string> projectNames, CancellationToken cancellationToken, Domain.Entities.GlobalSettings? globalSetting = null)
        {
            try
            {
                List<Domain.Entities.Project>? projects = new();
                globalSetting ??= await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSetting?.CountriesInfo);
                var projectList = (projectNames?.Any() ?? false) ? projectNames.Where(i => !string.IsNullOrWhiteSpace(i))?.Select(i => i.ToLower().Trim())?.ToList() : null;
                var allProjects = await _projectRepo.ListAsync(new GetNewProjectsByIdV2Spec(projectList ?? new List<string>()), cancellationToken);
                if (projectList?.Any() ?? false)
                {
                    foreach (var newProject in projectList)
                    {
                        Lrb.Domain.Entities.Project? existingProject = allProjects.FirstOrDefault(i => i.Name.ToLower().Trim() == newProject);
                        if (existingProject != null)
                        {
                            projects.Add(existingProject);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Project project = new()
                            {
                                Name = newProject,
                                MonetaryInfo = new ProjectMonetaryInfo
                                {
                                    Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                }
                            };
                            project = await _projectRepo.AddAsync(project, cancellationToken);
                            projects.Add(project);
                        }
                    }
                }
                return projects;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<(List<Domain.Entities.Lead> Leads, List<DuplicateLeadAssigmentResponseDto> SkippedLeadsInfo)> ReassignLeadsAsync(List<Domain.Entities.Lead> leads, LeadAssignmentDto assignmentDto, List<UserDetailsDto>? users, List<UserDetailsDto>? assignedUsers, CancellationToken cancellationToken = default, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? adminDetails = null, Guid? currentUserId = null, List<Domain.Entities.Project>? projects = null, Domain.Entities.GlobalSettings? globalSetting = null, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? userBasicDetails = null)
        {


            try
            {
                if (assignmentDto.UserIds.All(i => i == Guid.Empty))
                {
                    leads = await MakeLeadsAsUnAssignedAsync(leads, cancellationToken, currentUserId: currentUserId ?? assignmentDto.CurrentUserId, adminDetails, assignedUsers);
                    return (leads, new());
                }
                else
                {
                    if (users?.Any() ?? false)
                    {
                        var newStatus = await _customStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                        List<DuplicateLeadAssigmentResponseDto> skippedLeadsDtos = new();
                        Dictionary<Guid, List<Domain.Entities.Lead>> groupedAssignedLeads = new();
                        int i = 0;
                        int count = 0;
                        List<UserDetailsDto>? userDetails = new();
                        users.ForEach(i => userDetails.Add(i));
                        foreach (var lead in leads)
                        {
                            var user = users[i];
                            lead.BulkCategory = assignmentDto?.BulkCategory ?? BulkType.None;
                            if ((lead.AssignTo != user.Id) && lead.SecondaryUserId != user.Id)
                            {
                                #region UpdatingPickedDate

                                lead.ShouldUpdatePickedDate = false;
                                lead.PickedDate = null;
                                lead.IsPicked = false;

                                #endregion
                                var previousAssignedUser = lead.AssignedFrom == Guid.Empty ? null : lead.AssignedFrom;
                                lead.AssignedFrom = lead.AssignTo;
                                lead.AssignTo = user.Id;
                                lead.LastModifiedBy = currentUserId ?? _currentUserService.GetUserId();

                                // Set OriginalOwner to the assigned user when first assigned
                                if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                                {
                                    lead.OriginalOwner = lead.AssignTo;
                                }

                                if (groupedAssignedLeads.ContainsKey(lead.AssignTo) && (groupedAssignedLeads[lead.AssignTo]?.Any() ?? false))
                                {
                                    groupedAssignedLeads[lead.AssignTo].Add(lead);
                                }
                                else
                                {
                                    groupedAssignedLeads[lead.AssignTo] = new List<Domain.Entities.Lead>();
                                    groupedAssignedLeads[lead.AssignTo].Add(lead);
                                }

                                if (assignmentDto.UpdateProject && assignmentDto.Projects != null && assignmentDto.Projects.Any())
                                {
                                    await SetLeadProjectsAsync(lead, projects, cancellationToken);
                                }
                                await SetReassignedLeadEnquiryAsync(lead, assignmentDto, cancellationToken);

                                await SetReassignedLeadDetailsAsync(lead, assignmentDto, cancellationToken, newStatus);
                                try
                                {
                                    await UpdateReassignedLeadHistoryAsync(lead, assignmentDto.AssignmentType, userDetails, cancellationToken, currentUserId,previousAssignedUser:previousAssignedUser);
                                }
                                catch (Exception ex)
                                {

                                }
                                i = i == users.Count - 1 ? 0 : i + 1;
                            }
                            else
                            {
                                var existingSkippedLead = skippedLeadsDtos.FirstOrDefault(x => x.User?.Id == user.Id);
                                if (existingSkippedLead != null)
                                {
                                    existingSkippedLead.Leads?.Add(lead.Adapt<DuplicateAssigmentLeadDto>());
                                }
                                else
                                {
                                    var skippedLeadsDto = new DuplicateLeadAssigmentResponseDto()
                                    {
                                        User = new()
                                        {
                                            Id = user.Id,
                                            Name = user.FirstName + " " + user.LastName
                                        },
                                        Leads = new List<DuplicateAssigmentLeadDto> { lead.Adapt<DuplicateAssigmentLeadDto>() }
                                    };
                                    skippedLeadsDtos.Add(skippedLeadsDto);
                                }
                            }
                        }

                        foreach (var group in groupedAssignedLeads)
                        {
                            await SendLeadAssignmentNotificationsAsync(group.Value[0], group.Value.Count, cancellationToken, currentUserId, adminDetails, assignedUsers, globalSetting: globalSetting, userBasicInfo: userBasicDetails);
                        }

                        return (leads, skippedLeadsDtos);
                    }
                    else
                    {
                        throw new NotFoundException("No users found by the provided user ids.");
                    }
                }



            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<List<Domain.Entities.Lead>> MakeLeadsAsUnAssignedAsync(List<Domain.Entities.Lead> leads, CancellationToken cancellationToken, Guid? currentUserId = null, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? adminDetails = null, List<UserDetailsDto>? assignedUsers = null)
        {
            leads = leads.Where(i => i.AssignTo != Guid.Empty).ToList();

            #region UpdatingPickedDate
            leads.ForEach(lead =>
            {
                lead.ShouldUpdatePickedDate = false;
                lead.PickedDate = null;
                lead.IsPicked = false;
            });
            #endregion

            foreach (var lead in leads)
            {
                var previousAssignedUser = lead.AssignTo != Guid.Empty ? lead.AssignTo : lead.AssignedFrom == Guid.Empty ? null : lead.AssignedFrom;
                lead.AssignedFrom = lead.AssignTo;
                lead.AssignTo = Guid.Empty;
                lead.SecondaryUserId = Guid.Empty;
                lead.LastModifiedBy = currentUserId ?? _currentUserService.GetUserId();
                lead.LastModifiedOn = DateTime.UtcNow;
                //await _leadRepo.UpdateAsync(lead, cancellationToken);
                await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken, currentUserId: currentUserId, previousAssignedUser:previousAssignedUser);
            }
            if (leads.Any())
            {
                await SendLeadAssignmentNotificationsAsync(leads[0], leads.Count, cancellationToken, adminDetails: adminDetails, assignedUsers: assignedUsers);
            }
            return leads;
        }
        protected async Task SetReassignedLeadEnquiryAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default, ILeadRepositoryAsync? leadRepositoryAsync = null)
        {
            try
            {
                if (lead.Enquiries != null && lead.Enquiries.Any())
                {
                    var primaryEnquiry = lead.Enquiries.FirstOrDefault(e => e.IsPrimary);
                    if (primaryEnquiry != null)
                    {
                        if (assignmentDto.UpdateSubSource && !string.IsNullOrEmpty(assignmentDto.SubSource))
                        {
                            primaryEnquiry.SubSource = assignmentDto.SubSource;
                        }
                        if (assignmentDto.UpdateSource)
                        {
                            primaryEnquiry.LeadSource = assignmentDto.LeadSource;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task SetLeadProjectsAsync(Domain.Entities.Lead lead, List<Domain.Entities.Project>? projects, CancellationToken cancellationToken = default)
        {
            try
            {
                if (projects?.Any() ?? false)
                {
                    List<Domain.Entities.Project> newProjects = new List<Domain.Entities.Project>();
                    foreach (var project in projects)
                    {
                        newProjects.Add(project);
                    }
                    lead.Projects = newProjects;
                }
                else if ((lead?.Projects?.Any() ?? false) && projects == null)
                {
                    lead.Projects = projects;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetReassignedLeadDetailsAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken, CustomMasterLeadStatus? newStatus = null)
        {
            try
            {
                switch (assignmentDto.AssignmentType)
                {
                    case LeadAssignmentType.WithoutHistory:
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        lead.Notes = null;
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistory, assignmentDto, cancellationToken);
                        break;
                    case LeadAssignmentType.WithoutHistoryWithNewStatus:
                        await InitializeLeadStatusAsync(lead, cancellationToken, newStatus);
                        lead.ScheduledDate = null;
                        if (lead.CustomFlags != null)
                        {
                            lead.CustomFlags = null;
                        }
                        lead.Notes = null;
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistoryWithNewStatus, assignmentDto, cancellationToken);
                        break;
                    case LeadAssignmentType.WithHistory:
                        // await UpdateLeadAppointmentAndCommunicationAsync(lead, cancellationToken);
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory, assignmentDto, cancellationToken);
                        break;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateReassignedLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, List<UserDetailsDto> users, CancellationToken cancellationToken = default, Guid? currentUserId = null,Guid? previousAssignedUser = null)
        {
            var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ??  await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id,previousAssignedUser ?? currentUserId ?? Guid.Empty ));
            try
            {
                if (currentUserId != null && currentUserId != default && users != null && !(users.Any(i => i.Id == currentUserId)))
                {
                    var currentUser = await _userService.GetAsync(currentUserId?.ToString() ?? string.Empty, cancellationToken);
                    if (currentUser != null && users.Any(i => i.Id != currentUser.Id))
                    {
                        users.Add(currentUser);
                    }
                }
            }
            catch (Exception ex) { }
            var leadDto = lead.Adapt<ViewLeadDto>();
            if (leadDto != null)
            {
                leadDto.AssignmentType = assignmentType;
            }
            leadDto = await SetUsersInViewLeadDtoAsync(leadDto, _userService, cancellationToken, currentUserId);
            var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            if (existingLeadHistory != null)
            {
                try
                {
                    switch (assignmentType)
                    {
                        case LeadAssignmentType.WithoutHistory:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                            break;
                        case LeadAssignmentType.WithoutHistoryWithNewStatus:
                            await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId: currentUserId);
                            break;
                        case LeadAssignmentType.WithHistory:
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory));
                            break;
                    }
                }
                catch (Exception ex)
                {
                }
            }
            else
            {
                existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                if (existingLeadHistory != null)
                {
                    try
                    {
                        switch (assignmentType)
                        {
                            case LeadAssignmentType.WithoutHistory:
                                await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                                break;
                            case LeadAssignmentType.WithoutHistoryWithNewStatus:
                                await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId: currentUserId);
                                break;
                            case LeadAssignmentType.WithHistory:
                                await KeepOldHistoryForLeadReassignmentAsync(existingLeadHistory, leadDto, users, cancellationToken);
                                break;
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }
                else if (existingLeadHistory == null)
                {
                    existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new GetParentLeadHistorySpec(lead.Id));
                    if (existingLeadHistory != null)
                    {
                       existingLeadHistory.UserId = currentUserId ?? _currentUserService.GetUserId();
  
                        switch (assignmentType)
                        {
                            case LeadAssignmentType.WithoutHistory:
                                await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, false, cancellationToken, currentUserId: currentUserId);
                                break;
                            case LeadAssignmentType.WithoutHistoryWithNewStatus:
                                await AddNewHistoryForLeadReassignmentAsync(existingLeadHistory, newLeadHistory, users, leadDto, true, cancellationToken, currentUserId: currentUserId);
                                break;
                            case LeadAssignmentType.WithHistory:
                                var currentUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? _currentUserService.GetUserId()));
                                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignedFrom ?? Guid.Empty));
                                var assignToUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
                                existingLeadHistory = await UpdateHistoryForLeadReassignmentAsync(existingLeadHistory, assignedFromUser, assignToUser, currentUser);
                                if (existingLeadHistory != null)
                                {
                                    await _leadHistoryRepo.UpdateAsync(existingLeadHistory, cancellationToken);
                                }
                                await _leadHistoryRepo.AddAsync(newLeadHistory, cancellationToken);
                                break;
                        }
                    }
                    else
                    {

                        try
                        {
                            await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken);
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }
            }
        }
        protected async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, int leadCount, CancellationToken cancellationToken = default, Guid? currentUserId = null, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? adminDetails = null, List<UserDetailsDto>? assignedUsers = null, Domain.Entities.GlobalSettings? globalSetting = null, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? userBasicInfo = null)
        {
            try
            {
                var adminIds = adminDetails?.Select(i => i.Id).ToList();
                if (lead != null && lead.AssignTo == Guid.Empty && adminIds != null)
                {
                    adminIds = adminIds.Where(i => (currentUserId != null && i != currentUserId)).ToList();
                    if (adminIds.Any())
                    {
                        //foreach (var adminId in adminIds)
                        //{
                        //    //var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                        //    var adminDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { adminId.ToString() }, cancellationToken)).FirstOrDefault();
                        //    if (adminDetails != null)
                        //    {
                        //        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, adminId, null, noOfEntities: leadCount);
                        //    }
                        //}
                        List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, null, noOfEntities: leadCount, userIds: adminIds, globalSettings: globalSetting, currentUserId: currentUserId);
                    }
                }
                else
                {
                    //var assignedUser = await _userService.GetAsync(lead.AssignTo.ToString() ?? string.Empty, cancellationToken);
                    // var assignedUser = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { lead?.AssignTo.ToString() ?? string.Empty }, cancellationToken)).FirstOrDefault();
                    var assignedUser = assignedUsers?.FirstOrDefault(i => i.Id == lead?.AssignTo);
                    if (assignedUser != null && currentUserId != null && currentUserId != assignedUser.Id)
                    {
                        var userInfo = userBasicInfo?.FirstOrDefault(i => i.Id == lead?.AssignTo);
                        var notificationSetting = JsonConvert.DeserializeObject<NotificationSettings>(globalSetting?.NotificationSettings ?? string.Empty) ?? new();
                        if (leadCount > 1)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null, leadCount, globalSettings: globalSetting, currentUserId: currentUserId);
                            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminDetails?.Any() ?? false))
                            {
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignmentNotificationForAll, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, noOfEntities: leadCount, userIds: adminIds, globalSettings: globalSetting, currentUserId: currentUserId);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && (userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminDetails != null && adminDetails.Any(i => userInfo.ReportsTo.Id == i.Id)) && (userInfo.ReportsTo.Id != currentUserId))
                            {
                                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignmentNotificationForAll, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, noOfEntities: leadCount, userIds: userIds, globalSettings: globalSetting, currentUserId: currentUserId);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminDetails != null && adminDetails.Any(i => userInfo.GeneralManager.Id == i.Id)) && (userInfo.GeneralManager.Id != currentUserId))
                            {
                                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignmentNotificationForAll, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, noOfEntities: leadCount, userIds: userIds, globalSettings: globalSetting, currentUserId: currentUserId);
                            }
                        }
                        else
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, currentUserId: currentUserId);
                            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminDetails?.Any() ?? false))
                            {
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, userIds: adminIds);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && ((userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminDetails != null && adminDetails.Any(i => userInfo.ReportsTo.Id == i.Id)) && (userInfo.ReportsTo.Id != currentUserId)))
                            {
                                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, userIds: userIds);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminDetails != null && adminDetails.Any(i => userInfo.GeneralManager.Id == i.Id)) && (userInfo.GeneralManager.Id != currentUserId))
                            {
                                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, userIds: userIds);

                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = moduleName
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            catch
            {
                throw;
            }
        }
        protected async Task UpdateLeadHistoryAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false,Guid? previousAssignedUser = null)
        {
            try
            {
                var userId = currentUserId ?? _currentUserService.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken, currentUserId: userId);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = previousAssignedUser ?? lead.AssignedFrom ?? userId;
                    var existingLeadHistory = (await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId))) ?? await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, previousAssignedUser ?? lead.AssignedFrom ?? Guid.Empty)) ;
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {


                        await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);

                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo)) ?? await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id,previousAssignedUser ?? userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory), cancellationToken);
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<bool> CreateLeadAssignmentHistory(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, LeadAssignmentDto? assignmentDto = null, CancellationToken cancellationToken = default, bool isDuplicate = default)
        {
            var leadAssignmentHistories = await _leadAssignmentRepo.ListAsync(new GetLeadAssignmentsByIdSpecs(lead.Id));
            if (leadAssignmentHistories == null)
            {
                var assignment = new LeadAssignment()
                {
                    AssignTo = lead.AssignTo,
                    AssignedFrom = lead.AssignedFrom,
                    Notes = lead.Notes,
                    LeadId = lead.Id,
                    UserId = lead.AssignTo,
                    LeadAssignmentType = assignmentType,
                    AssignmentDate = DateTime.UtcNow,
                    LastModifiedBy = lead.LastModifiedBy,
                    CreatedBy = lead.LastModifiedBy
                };
                if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignTo = lead.SecondaryUserId;
                }
                if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                {
                    assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                }
                if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                {
                    assignment.ProjectName = assignmentDto?.Projects?.ToString();
                    assignment.SourceName = assignmentDto?.LeadSource.ToString();
                }
                if (isDuplicate)
                {
                    assignment.IsDuplicate = true;
                }
                await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
            }
            else
            {
                var leadLastAssignment = leadAssignmentHistories?.LastOrDefault();
                if (leadLastAssignment?.AssignTo != lead?.AssignTo)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                        LastModifiedBy = lead.LastModifiedBy,
                        CreatedBy = lead.LastModifiedBy
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.ToList().ToString();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                }
                else if (lead?.SecondaryUserId != null && leadLastAssignment?.SecondaryAssignTo != lead.SecondaryUserId)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                        LastModifiedBy = lead.LastModifiedBy,
                        CreatedBy = lead.LastModifiedBy
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.ToString();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    await _leadAssignmentRepo.AddAsync(assignment, cancellationToken);
                }
            }
            return true;
        }
        protected async Task InitializeLeadStatusAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, CustomMasterLeadStatus? newStatus = null)
        {
            try
            {
                if (lead != null && newStatus != null)
                {
                    //var newStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                    lead.CustomLeadStatus = newStatus;
                    //if (newStatus?.MasterLeadStatusId != null)
                    //{
                    //    lead.Status = await _leadStatusRepo.FirstOrDefaultAsync(new GetMasterLeadStatusByIdSpec(newStatus.MasterLeadStatusId.Value), cancellationToken);
                    //}
                }
                else if (lead != null)
                {
                    lead.CustomLeadStatus = newStatus;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task AddNewHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, LeadHistory newHistory, List<UserDetailsDto> users, ViewLeadDto fullLeadDto, bool withNewStatus, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                var currentUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? _currentUserService.GetUserId()));
                if (currentUser == null)
                {
                    try
                    {
                        currentUser = (await _userService.GetListOfUsersByIdsAsync(new() { (currentUserId ?? _currentUserService.GetUserId()).ToString() }, cancellationToken)).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {

                    }
                }
                var assignedFromUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignedFrom ?? Guid.Empty));
                var assignToUser = users?.FirstOrDefault(i => i.Id == (fullLeadDto?.AssignTo ?? Guid.Empty));
                existingLeadHistory = await UpdateHistoryForLeadReassignmentAsync(existingLeadHistory, assignedFromUser, assignToUser, currentUser, withNewStatus, fullLeadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(existingLeadHistory, cancellationToken);
                }
                var leadHistory = existingLeadHistory.MapV1LeadHistory(currentUser, assignedFromUser, assignToUser, withNewStatus);
                leadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newHistory, leadHistory, withNewStatus);
                if (leadHistory != null && leadHistory?.Notes != null &&
                            leadHistory.CurrentVersion >= 1 &&
                            (!string.IsNullOrEmpty(leadHistory.Notes.FirstOrDefault().Value)) && (!string.Equals(leadHistory?.LastModifiedBy?.First().Value?.Trim(), "Integration", StringComparison.OrdinalIgnoreCase)))
                {

                    leadHistory.Notes[leadHistory.Notes.FirstOrDefault().Key] = "";

                }
                await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        private async Task<LeadHistory?> UpdateHistoryForLeadReassignmentAsync(LeadHistory leadHistory, UserDetailsDto? assignedFromUser, UserDetailsDto? assignToUser, UserDetailsDto? currentUser,bool? isWithNewStatus = false,ViewLeadDto? leadDto = null)
        {
            try
            {
                if (leadHistory != null)
                {
                    var version = leadHistory.CurrentVersion + 1;
                    leadHistory.LastModifiedBy?.Add(version, $"{currentUser?.FirstName} {currentUser?.LastName}" ?? string.Empty);
                    if (leadHistory?.AssignedTo?.LastOrDefault().Value != assignToUser?.Id)
                    {
                        leadHistory.AssignedTo?.Add(version, assignToUser?.Id ?? default);
                    }
                    var assignToName = $"{assignToUser?.FirstName} {assignToUser?.LastName}".Trim();
                    if (leadHistory?.AssignedToUser?.LastOrDefault().Value != assignToName)
                    {
                        leadHistory.AssignedToUser?.Add(version, string.IsNullOrWhiteSpace(assignToName) ? string.Empty : assignToName);
                    }
                    var assignFromName = $"{assignedFromUser?.FirstName} {assignedFromUser?.LastName}".Trim();
                    if (leadHistory?.AssignedFromUser?.LastOrDefault().Value != assignFromName)
                    {
                        leadHistory.AssignedFromUser?.Add(version, string.IsNullOrWhiteSpace(assignFromName) ? string.Empty : assignFromName);
                    }

                    if (isWithNewStatus == true && leadDto != null && leadDto.Status != null && (!string.IsNullOrEmpty(leadDto.Status.DisplayName)) && (!(string.IsNullOrEmpty(leadHistory?.BaseLeadStatus?.LastOrDefault().Value)) && leadHistory?.BaseLeadStatus?.LastOrDefault().Value != leadDto.Status?.DisplayName ))
                    {
                        leadHistory.BaseLeadStatus?.Add(version, leadDto.Status?.DisplayName ?? string.Empty);
                    }
                    leadHistory.ModifiedDate?.Add(version, DateTime.UtcNow);
                    leadHistory.CurrentVersion = version;
                }
                return leadHistory;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        public async Task KeepOldHistoryForLeadReassignmentAsync(LeadHistory existingLeadHistory, ViewLeadDto leadDto, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            try
            {
                //await leadDto.SetUsersInViewLeadDtoAsync(users, CancellationToken.None);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                if (existingLeadHistory != null)
                {
                    await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory));
                }
                else
                {
                    await _leadHistoryRepo.AddAsync(leadHistory);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<ViewLeadDto> GetFullLeadDtoAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken = default, IUserService? userService = null, Guid? currentUserId = null)
        {
            try
            {

                // var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = lead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                leadDto = await SetUsersInViewLeadDtoAsync(leadDto, userService, cancellationToken, currentUserId);
                return leadDto;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        private async Task<ViewLeadDto?> SetUsersInViewLeadDtoAsync(ViewLeadDto? leadDto, IUserService userService, CancellationToken cancellationToken, Guid? currentUserId = null)
        {
            if (leadDto == null) { return null; }
            var userIds = new List<string>()
            {
                leadDto?.AssignTo?.ToString() ?? string.Empty,
                leadDto?.SecondaryUserId?.ToString() ?? string.Empty,
                leadDto?.LastModifiedBy.ToString() ?? string.Empty,
                leadDto?.AssignedFrom.ToString() ?? string.Empty,
                leadDto?.SourcingManager?.ToString() ?? string.Empty,
                leadDto?.ClosingManager?.ToString() ?? string.Empty,
                leadDto?.BookedBy?.ToString() ?? string.Empty,
                currentUserId?.ToString() ?? string.Empty,
                leadDto?.SecondaryFromUserId?.ToString() ?? string.Empty,
            };
            var users = await _userService.GetListOfUsersByIdsAsync(userIds?.Distinct()?.ToList() ?? new List<string>(), cancellationToken);
            UserDetailsDto? assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
            //try
            //{
            //    assignedUser = await _userService.GetAsync(leadDto?.AssignTo?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser?.Adapt<UserDto>();
                leadDto.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;

            }
            UserDetailsDto? secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryUserId ?? Guid.Empty));
            //try
            //{
            //    secondaryUser = await _userService.GetAsync(leadDto?.SecondaryUserId?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser?.Adapt<UserDto>();
                leadDto.SecondaryUser.Name = secondaryUser?.FirstName + " " + secondaryUser?.LastName;

            }
            UserDetailsDto? lastModifiedByUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? leadDto?.LastModifiedBy));
            //try
            //{
            //    lastModifiedByUser = await _userService.GetAsync(leadDto?.LastModifiedBy.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (lastModifiedByUser != null)
            {
                leadDto.LastModifiedByUser = lastModifiedByUser?.Adapt<UserDto>();
                leadDto.LastModifiedByUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }

            UserDetailsDto? secondaryFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryFromUserId ?? Guid.Empty));
            if (secondaryFromUser != null)
            {
                leadDto.SecondaryFromUser = secondaryFromUser?.Adapt<UserDto>();
                leadDto.SecondaryFromUser.Name = secondaryFromUser?.FirstName + " " + secondaryFromUser?.LastName;
            }

            UserDetailsDto? assignedFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignedFrom ?? Guid.Empty));
            //try
            //{
            //    assignedFromUser = await _userService.GetAsync(leadDto?.AssignedFrom.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (assignedFromUser != null)
            {
                leadDto.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                leadDto.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
            }

            UserDetailsDto? sourcingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.SourcingManager ?? Guid.Empty));
            //try
            //{
            //    sourcingManager = await _userService.GetAsync(leadDto?.SourcingManager?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (sourcingManager != null)
            {
                leadDto.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                leadDto.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
            }
            UserDetailsDto? closingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.ClosingManager ?? Guid.Empty));
            //try
            //{
            //    closingManager = await _userService.GetAsync(leadDto?.ClosingManager?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (closingManager != null)
            {
                leadDto.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                leadDto.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
            }
            UserDetailsDto? bookedByUser = users?.FirstOrDefault(i => i.Id == (leadDto?.BookedBy ?? Guid.Empty));
            //try
            //{
            //    bookedByUser = await _userService.GetAsync(leadDto?.BookedBy?.ToString() ?? string.Empty, cancellationToken);

            //}
            //catch (NotFoundException e) { }
            if (bookedByUser != null)
            {
                leadDto.BookedByUser = bookedByUser?.Adapt<UserDto>();
                leadDto.BookedByUser.Name = bookedByUser?.FirstName + " " + bookedByUser?.LastName;
            }
            return leadDto;
        }

        protected async Task<(List<Domain.Entities.Lead> Leads, List<DuplicateLeadAssigmentResponseDto> SkippedLeadsDtos, List<LeadHistory> NewHistories, List<LeadHistory> OldHistories)> CreateDuplicateLeadsAsync(List<Domain.Entities.Lead> leads, LeadAssignmentDto assignmentDto, List<UserDetailsDto> users, CancellationToken cancellationToken = default, List<Domain.Entities.Project>? projects = null, Guid? currentUserId = null, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? adminDetails = null, List<UserDetailsDto>? assignedUsers = null, Domain.Entities.GlobalSettings? globalSetting = null, List<Lrb.Application.UserDetails.Web.UserBasicInfoDto>? userBasicDetails = null)
        {
            var primaryContactNos = leads.Select(i => i.ContactNo).ToList();
            var rootLeads = await _leadRepo.ListAsync(new GetRootLeadSpec(primaryContactNos), cancellationToken);
            try
            {
                List<Domain.Entities.Lead> duplicateLeads = new();
                List<LeadHistory> newHistories = new();
                List<LeadHistory> oldHistories = new();
                List<DuplicateLeadAssigmentResponseDto> skippedLeadsDtos = new();
                var newStatus = await _customStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec("new"), cancellationToken);
                foreach (var user in users)
                {
                    var skippedLeadsDto = new DuplicateLeadAssigmentResponseDto()
                    {
                        User = new()
                        {
                            Id = user.Id,
                            Name = user.FirstName + " " + user.LastName
                        },
                        Leads = new()
                    };

                    List<Domain.Entities.Lead> userBasedDuplicateLeads = new();
                    List<LeadHistory> userBasedNewHistories = new();
                    List<LeadHistory> userBasedOldHistories = new();

                    foreach (Domain.Entities.Lead lead in leads)
                    {
                        var alreadyAssignedLeads = await _leadRepo.ListAsync(new DuplicateLeadsForAssignmentSpec(lead.ContactNo, user.Id), cancellationToken);

                        if (alreadyAssignedLeads != null && alreadyAssignedLeads.Any())
                        {
                            skippedLeadsDto.Leads.Add(alreadyAssignedLeads[0].Adapt<DuplicateAssigmentLeadDto>());
                        }
                        else
                        {
                            var duplicateLeadInfo = await CreateDuplicateLeadAsync(lead, assignmentDto, user, cancellationToken, newStatus, projects, currentUserId);

                            userBasedDuplicateLeads.Add(duplicateLeadInfo.Lead);
                            await UpdateDuplicateVersionAsync(duplicateLeadInfo.Lead, rootLeads, lead.Id, currentUserId, cancellationToken);
                            if (duplicateLeadInfo.OldHistory != null)
                            {
                                userBasedOldHistories.Add(duplicateLeadInfo.OldHistory);
                                await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory, null, cancellationToken, true);
                            }
                            else if (duplicateLeadInfo.NewHistory != null)
                            {
                                userBasedNewHistories.Add(duplicateLeadInfo.NewHistory);
                                await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithoutHistory, null, cancellationToken, true);
                            }

                        }
                    }
                    if (userBasedDuplicateLeads.Any())
                    {
                        #region UpdatingPickedDate
                        userBasedDuplicateLeads.ForEach(lead =>
                        {
                            lead.ShouldUpdatePickedDate = false;
                            lead.PickedDate = null;
                            lead.IsPicked = false;
                        });
                        #endregion
                        await _leadRepo.UpdateRangeAsync(rootLeads);

                        await _leadRepo.AddRangeAsync(userBasedDuplicateLeads, cancellationToken);

                        await _leadHistoryRepo.AddRangeAsync(userBasedNewHistories, cancellationToken);

                        await _leadHistoryRepo.UpdateRangeAsync(userBasedOldHistories, cancellationToken);

                        await SendLeadAssignmentNotificationsAsync(userBasedDuplicateLeads[0], userBasedDuplicateLeads.Count, cancellationToken, adminDetails: adminDetails, assignedUsers: assignedUsers, globalSetting: globalSetting, currentUserId: currentUserId);

                        duplicateLeads.AddRange(userBasedDuplicateLeads);
                        newHistories.AddRange(userBasedNewHistories);
                        oldHistories.AddRange(userBasedOldHistories);
                    }
                    skippedLeadsDtos.Add(skippedLeadsDto);
                }
                return (duplicateLeads, skippedLeadsDtos, newHistories, oldHistories);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(Domain.Entities.Lead Lead, LeadHistory? NewHistory, LeadHistory? OldHistory)> CreateDuplicateLeadAsync(Domain.Entities.Lead lead, LeadAssignmentDto assignmentDto, UserDetailsDto user, CancellationToken cancellationToken = default, CustomMasterLeadStatus? newStatus = null, List<Domain.Entities.Project>? projects = null, Guid? currentUserId = null)
        {
            try
            {
                var leadDto = await GetFullLeadDtoAsync(lead, cancellationToken, _userService, currentUserId);

                var duplicateLead = await InitializeDuplicateLeadAsync(leadDto, user, assignmentDto, currentUserId, cancellationToken, newStatus);

                if (leadDto.Properties != null && leadDto.Properties.Any())
                {
                    await SetTruePropertiesAsync(duplicateLead, leadDto.Properties, cancellationToken);
                }

                if (assignmentDto.UpdateProject && assignmentDto.Projects != null && assignmentDto.Projects.Any())
                {
                    await SetLeadProjectsAsync(duplicateLead, projects, cancellationToken);
                }
                else
                {
                    await SetTrueProjectsAsync(duplicateLead, leadDto.Projects, cancellationToken);
                }
                if (duplicateLead.Agencies?.Any() ?? false)
                {
                    var agencies = duplicateLead.Agencies?.Select(i => i.Name ?? string.Empty).ToList();
                    await SetLeadAgencyAsync(duplicateLead, agencies);
                }
                if (duplicateLead.ChannelPartners?.Any() ?? false)
                {
                    var channelPartners = duplicateLead.ChannelPartners?.Select(i => i.FirmName ?? string.Empty).ToList();
                    await SetChannelPartnersAsync(duplicateLead, channelPartners);
                }
                if (duplicateLead.Campaigns?.Any() ?? false)
                {
                    var campaigns = duplicateLead.Campaigns?.Select(i => i.Name ?? string.Empty).ToList();
                    await SetCampaignPartnersAsync(duplicateLead, campaigns);
                }
                await SetDuplicateLeadEnquiryAsync(duplicateLead, leadDto.Enquiry, assignmentDto, cancellationToken);

                var (New, Old) = await CreateDuplicateLeadHistoryAsync(duplicateLead, assignmentDto.AssignmentType, leadDto.Id, leadDto.AssignTo ?? Guid.Empty, cancellationToken, currentUserId);
                if (duplicateLead.Enquiries?.Any() ?? false)
                {
                    foreach (var item in duplicateLead.Enquiries)
                    {
                        if (item.Address != null)
                        {
                            item.Address = null;
                        }
                    }
                }

                return (duplicateLead, New, Old);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<Domain.Entities.Lead> InitializeDuplicateLeadAsync(ViewLeadDto leadDto, Identity.Users.UserDetailsDto user, LeadAssignmentDto assignmentDto, Guid? currentUserId = null, CancellationToken cancellationToken = default, CustomMasterLeadStatus? newStatus = null)
        {
            try
            {
                Guid rootId = leadDto.RootId.HasValue ? leadDto.RootId.Value : leadDto.Id;
                Guid parentLeadId = leadDto.Id;
                var duplicateLead = leadDto.Adapt<Domain.Entities.Lead>();
                duplicateLead.Id = Guid.NewGuid();
                duplicateLead.CreatedBy = default;
                duplicateLead.ChildLeadsCount = 0;
                duplicateLead.DuplicateLeadVersion = null;
                duplicateLead.SecondaryUserId = null;
                duplicateLead.LastModifiedBy = default;
                //await UpdateDuplicateVersionAsync(duplicateLead, leadDto.Id, cancellationToken);
                duplicateLead.Projects = new List<Domain.Entities.Project>();
                duplicateLead.Properties = new List<Domain.Entities.Property>();
                duplicateLead.AssignTo = user.Id;
                duplicateLead.Appointments = null;
                duplicateLead.CustomFlags = null;
                duplicateLead.Enquiries = new List<LeadEnquiry>();
                duplicateLead.LastModifiedBy = currentUserId ?? _currentUserService.GetUserId();
                duplicateLead.CreatedBy = currentUserId ?? _currentUserService.GetUserId();
                if (assignmentDto.AssignmentType == LeadAssignmentType.WithoutHistoryWithNewStatus)
                {
                    await InitializeLeadStatusAsync(duplicateLead, cancellationToken, newStatus);
                }
                else
                {
                    string status = string.Empty;
                    if (leadDto?.Status?.ChildType == null)
                    {
                        status = leadDto?.Status?.Status ?? string.Empty;
                    }
                    else
                    {
                        status = leadDto?.Status?.ChildType?.Status ?? string.Empty;
                    }
                    duplicateLead.CustomLeadStatus = !string.IsNullOrWhiteSpace(status) ? newStatus : null;
                    // var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new LeadStatusSpec(new List<Guid>() { duplicateLead.CustomLeadStatus?.Id ?? Guid.Empty }), cancellationToken);
                    //if (customStatus?.MasterLeadStatusId != null)
                    //{
                    //    duplicateLead.Status = await _leadStatusRepo.FirstOrDefaultAsync(new GetMasterLeadStatusByIdSpec(customStatus.MasterLeadStatusId.Value), cancellationToken);
                    //}
                    //else

                    //    duplicateLead.Status = null;
                    //}


                }
                if (!(assignmentDto.AssignmentType == LeadAssignmentType.WithHistory))
                {
                    duplicateLead.Notes = null;
                    duplicateLead.ConfidentialNotes = null;
                    duplicateLead.Rating = null;
                    duplicateLead.ScheduledDate = null;
                    duplicateLead.TagInfo = new LeadTag();
                    duplicateLead.CustomFlags = null;
                }
                return duplicateLead;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        private async Task SetTruePropertiesAsync(Domain.Entities.Lead lead, List<PropertyDto>? properties, CancellationToken cancellationToken = default)
        {
            try
            {
                var trueProperties = properties?.Where(i => !string.IsNullOrWhiteSpace(i.Title)).ToList();
                if (trueProperties != null && trueProperties.Any())
                {
                    var propertyTitles = trueProperties.Select(i => i.Title ?? "Invalid")?.ToList();
                    var newProperties = await _propertyRepo?.ListAsync(new GetAllPropertiesForIntegrationSpec(propertyTitles), cancellationToken);
                    lead.Properties = newProperties;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        private async Task SetTrueProjectsAsync(Domain.Entities.Lead lead, List<ProjectDto>? projects, CancellationToken cancellationToken = default)
        {
            try
            {
                var trueProjects = projects?.Where(i => !string.IsNullOrWhiteSpace(i.Name)).ToList();
                if (trueProjects != null && trueProjects.Any())
                {
                    var projectNames = trueProjects.Select(i => i.Name ?? "Invalid")?.ToList();
                    var newProjects = await _projectRepo.ListAsync(new V2GetAllProjectsForIntegrationSpec(projectNames), cancellationToken);
                    lead.Projects = newProjects;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        private async Task SetDuplicateLeadEnquiryAsync(Domain.Entities.Lead lead, ViewLeadEnquiryDto? enquiryDto, LeadAssignmentDto assignmentDto, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Enquiries ??= new List<LeadEnquiry>();
                var duplicateEnquiry = new LeadEnquiry();
                if (enquiryDto != null)
                {
                    duplicateEnquiry = enquiryDto.Adapt<LeadEnquiry>();
                    if (enquiryDto.PropertyType != null && enquiryDto.PropertyType.ChildType != null)
                    {
                        duplicateEnquiry.PropertyType = await ValidatePropertyTypeAsync(enquiryDto.PropertyType?.ChildType?.Id ?? Guid.Empty, cancellationToken);
                    }
                    if (enquiryDto.PropertyTypes != null && enquiryDto.PropertyTypes.Select(i=>i.ChildType).Count() > 0)
                    {
                        duplicateEnquiry.PropertyTypes = await ValidatePropertyTypesAsync(enquiryDto.PropertyTypes.Select(i => i.ChildType.Id).ToList(), cancellationToken);
                    }
                    //if (duplicateEnquiry.Address != null)
                    //{
                    //    var locationId = duplicateEnquiry.Address.LocationId;
                    //    duplicateEnquiry.Address.Id = Guid.NewGuid();
                    //    duplicateEnquiry.Address.Location = null;
                    //    duplicateEnquiry.Address.LocationId = locationId;
                    //}
                    if (duplicateEnquiry.Addresses != null)
                    {
                        foreach (var address in duplicateEnquiry.Addresses)
                        {
                            if (address != null)
                            {
                                var locationId = address.LocationId;
                                address.Id = Guid.NewGuid();
                                address.Location = null;
                                address.LocationId = locationId;
                            }
                        }
                    }
                }
                if (assignmentDto.UpdateSource)
                {
                    duplicateEnquiry.LeadSource = assignmentDto.LeadSource;
                }
                if (assignmentDto.UpdateSubSource && !string.IsNullOrEmpty(assignmentDto.SubSource))
                {
                    duplicateEnquiry.SubSource = assignmentDto.SubSource;
                }
                duplicateEnquiry.Id = Guid.NewGuid();
                lead.Enquiries.Add(duplicateEnquiry);

            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(LeadHistory? New, LeadHistory? Old)> CreateDuplicateLeadHistoryAsync(Domain.Entities.Lead lead, LeadAssignmentType assignmentType, Guid parentLeadId, Guid parentAssignTo, CancellationToken cancellationToken = default, Guid? currentUserId = null)
        {
            try
            {
                (LeadHistory? New, LeadHistory? Old) historyData = new();
                LeadHistory leadHistory;
                if (assignmentType == LeadAssignmentType.WithHistory)
                {
                    var parentLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(parentLeadId, parentAssignTo));
                    if (parentLeadHistory != null)
                    {
                        leadHistory = parentLeadHistory.Adapt<LeadHistory>();
                        var leadDto = lead.Adapt<ViewLeadDto>();
                        await SetUsersInViewLeadDtoAsync(leadDto, _userService, cancellationToken, currentUserId);
                        if (currentUserId != null)
                        {
                            leadHistory.LastModifiedByUser[leadHistory.CurrentVersion] = leadDto?.LastModifiedByUser?.Id ?? leadHistory.LastModifiedByUser[leadHistory.CurrentVersion];
                            leadHistory.LastModifiedBy[leadHistory.CurrentVersion] = leadDto?.LastModifiedByUser?.Name ?? leadHistory.LastModifiedBy[leadHistory.CurrentVersion];
                        }
                        if (leadHistory?.AssignedTo?.LastOrDefault().Value != leadDto?.AssignTo)
                        {
                            if (leadHistory?.AssignedTo != null)
                            {
                                leadHistory.AssignedTo[leadHistory.CurrentVersion] = leadDto?.AssignTo ?? leadHistory.AssignedTo[leadHistory.CurrentVersion];
                            }
                            else
                            {
                                leadHistory.AssignedTo = new Dictionary<int, Guid>() { { leadHistory.CurrentVersion, leadDto?.AssignTo ?? Guid.Empty } };
                            }
                        }
                        if (!(string.IsNullOrEmpty(leadHistory?.AssignedToUser?.LastOrDefault().Value)) && leadHistory?.AssignedToUser?.LastOrDefault().Value != leadDto?.AssignedUser?.Name)
                        {
                            if (leadHistory?.AssignedToUser != null)
                            {
                                leadHistory.AssignedToUser[leadHistory.CurrentVersion] = leadDto?.AssignedUser?.Name ?? leadHistory.AssignedToUser[leadHistory.CurrentVersion];
                            }
                            else
                            {
                                leadHistory.AssignedToUser = new Dictionary<int, string>() { { leadHistory.CurrentVersion, leadDto?.AssignedUser?.Name } };
                            }
                        }
                        if (!(string.IsNullOrEmpty(leadHistory?.AssignedFromUser?.LastOrDefault().Value)) && leadHistory?.AssignedFromUser?.LastOrDefault().Value != leadDto?.AssignedFromUser?.Name)
                        {
                            if (leadHistory?.AssignedFromUser != null)
                            {
                                leadHistory.AssignedFromUser[leadHistory.CurrentVersion] = leadDto?.AssignedFromUser?.Name ?? leadHistory.AssignedFromUser[leadHistory.CurrentVersion];
                            }
                            else
                            {
                                leadHistory.AssignedFromUser = new Dictionary<int, string>() { { leadHistory.CurrentVersion, leadDto?.AssignedFromUser?.Name } };
                            }
                        }
                        leadHistory.CreatedDate = DateTime.UtcNow;
                        leadHistory.ModifiedDate[leadHistory.CurrentVersion] = DateTime.UtcNow;
                        leadHistory.LeadId = lead.Id;
                        leadHistory.UserId = lead.AssignTo;
                        leadHistory.DuplicateLeadVersion = lead.DuplicateLeadVersion;
                        leadHistory.Id = Guid.NewGuid();
                        historyData.New = leadHistory;
                    }
                    else
                    {
                        historyData = await GetUpdatedLeadHistoryAsync(lead, cancellationToken, currentUserId);
                        if (historyData.New != null && historyData.New.ModifiedDate != null)
                        {
                            historyData.New.ModifiedDate[historyData.New.CurrentVersion] = DateTime.UtcNow;
                        }
                    }
                }
                else
                {
                    lead.Notes = null;
                    lead.Rating = null;
                    lead.ScheduledDate = null;
                    lead.TagInfo = new LeadTag();
                    lead.CustomFlags = null;
                    historyData = await GetUpdatedLeadHistoryAsync(lead, cancellationToken, currentUserId);
                }
                return historyData;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task UpdateDuplicateVersionAsync(Domain.Entities.Lead lead, List<Domain.Entities.Lead>? rootLeads, Guid? parentLeadId = null, Guid? currentUserId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var rootLead = rootLeads?.FirstOrDefault(i => i.ContactNo == lead.ContactNo);
                if (rootLead != null)
                {
                    lead.RootId = rootLead.Id;
                    lead.DuplicateLeadVersion = "D" + (rootLead.ChildLeadsCount + 1);
                    lead.ParentLeadId = parentLeadId != null ? parentLeadId : rootLead.Id;
                    lead.LastModifiedBy = currentUserId ?? _currentUserService.GetUserId();
                    rootLead.ChildLeadsCount += 1;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<MasterPropertyType?> ValidatePropertyTypeAsync(Guid? propertyTypeId, CancellationToken cancellationToken = default)
        {
            try
            {
                MasterPropertyType? propertyType = null;
                if (propertyTypeId != Guid.Empty && propertyTypeId != null)
                {
                    propertyType = await _propertyTypeRepo.GetByIdAsync(propertyTypeId ?? Guid.Empty, cancellationToken);
                    if (propertyType == null)
                    {
                        throw new InvalidDataException("Property type Id does not belong to master data");
                    }
                }
                return propertyType;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<List<MasterPropertyType>?> ValidatePropertyTypesAsync(List<Guid>? propertyTypeIds, CancellationToken cancellationToken = default)
        {
            try
            {
                List<MasterPropertyType>? propertyType = null;

                if (propertyTypeIds != null && propertyTypeIds.Any())
                {
                    propertyType = await _propertyTypeRepo.ListAsync(new Lrb.Application.Property.Web.Specs.GetMasterPropertyTypeSpec(propertyTypeIds), cancellationToken);
                }

                return propertyType;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public async Task<(LeadHistory? New, LeadHistory? Old)> GetUpdatedLeadHistoryAsync(Domain.Entities.Lead lead, CancellationToken cancellationToken, Guid? currentUserId = null)
        {
            try
            {
                var leadDto = lead.Adapt<ViewLeadDto>();
                await SetUsersInViewLeadDtoAsync(leadDto, _userService, cancellationToken, currentUserId);
                var newLeadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                if (existingLeadHistory != null)
                {
                    var updatedLeadHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, newLeadHistory);
                    return (null, updatedLeadHistory);
                }
                else
                {
                    return (newLeadHistory, null);
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadAgencyAsync(Domain.Entities.Lead lead, List<string>? agencyList, string? agencyName = null, Guid? templateId = null, CancellationToken cancellationToken = default)
        {
            try
            {
                lead.Agencies = null;
                var template = await _qrFormTemplateRepo.GetByIdAsync(templateId ?? Guid.Empty, cancellationToken);

                if (!string.IsNullOrWhiteSpace(template?.Agency?.Name ?? string.Empty))
                {
                    if (agencyList?.Any() ?? false)
                    {
                        agencyList?.Add(template.Agency.Name);
                    }
                    else
                    {
                        agencyList = new() { template.Agency.Name };
                    }
                }

                //if (!string.IsNullOrWhiteSpace(agencyName))
                //{
                //    if (agencyList?.Any() ?? false)
                //    {
                //        agencyList?.Add(agencyName);
                //    }
                //    else
                //    {
                //        agencyList = new() { agencyName };
                //    }
                //}

                List<Lrb.Domain.Entities.Agency>? agencies = new();
                agencyList = (agencyList?.Any() ?? false) ? agencyList.Where(i => !string.IsNullOrWhiteSpace(i)).Distinct().ToList() : null;
                if (agencyList?.Any() ?? false)
                {
                    foreach (var newAgency in agencyList)
                    {
                        Lrb.Domain.Entities.Agency? existingAgency = (await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(newAgency), cancellationToken));
                        if (existingAgency != null)
                        {
                            agencies.Add(existingAgency);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Agency agency = new() { Name = newAgency };
                            agency = await _agencyRepo.AddAsync(agency, cancellationToken);
                            agencies.Add(agency);
                        }
                    }
                    lead.Agencies = agencies;
                }
                else if ((lead?.Agencies?.Any() ?? false) && agencyList == null)
                {
                    lead.Agencies = agencies;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetChannelPartnersAsync(Domain.Entities.Lead lead, List<string>? channelPartnersList, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Lrb.Domain.Entities.ChannelPartner> channelPartners = new();
                channelPartnersList = (channelPartnersList?.Any() ?? false) ? channelPartnersList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (channelPartnersList?.Any() ?? false)
                {
                    foreach (var newChannelPartner in channelPartnersList)
                    {
                        var existingChannelPartner = (await _cpRepository.ListAsync(new GetChannelPartnerByNameSpecs(newChannelPartner), cancellationToken)).FirstOrDefault();
                        if (existingChannelPartner != null)
                        {
                            channelPartners.Add(existingChannelPartner);
                        }
                        else
                        {
                            Lrb.Domain.Entities.ChannelPartner channelPartner = new() { FirmName = newChannelPartner };
                            channelPartner = await _cpRepository.AddAsync(channelPartner, cancellationToken);
                            channelPartners.Add(channelPartner);
                        }
                    }
                    lead.ChannelPartners = channelPartners;
                }
                else if ((lead?.ChannelPartners?.Any() ?? false) && channelPartnersList == null)
                {
                    lead.ChannelPartners = channelPartners;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }



        protected async Task SetCampaignPartnersAsync(Domain.Entities.Lead lead, List<string>? campaignList, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Lrb.Domain.Entities.Campaign> campaigns = new();
                campaignList = (campaignList?.Any() ?? false) ? campaignList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (campaignList?.Any() ?? false)
                {
                    foreach (var newCampaign in campaignList)
                    {
                        var existingCampaign = (await _campaignRepository.ListAsync(new GetCampaignByNameSpec(newCampaign), cancellationToken)).FirstOrDefault();
                        if (existingCampaign != null)
                        {
                            campaigns.Add(existingCampaign);
                        }
                        else
                        {
                            Lrb.Domain.Entities.Campaign campaign = new() { Name = newCampaign };
                            campaign = await _campaignRepository.AddAsync(campaign, cancellationToken);
                            campaigns.Add(campaign);
                        }
                    }
                    lead.Campaigns = campaigns;
                }
                else if ((lead?.Campaigns?.Any() ?? false) && campaignList == null)
                {
                    lead.Campaigns = campaigns;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(LeadHistory,LeadHistory)> UpdateLeadHistoryV1Async(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false)
        {
            try
            {
                var userId = currentUserId ?? _currentUserService.GetUserId();
                LeadHistory? oldHistory = null;
                LeadHistory? newHistory = null;
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken, currentUserId: userId);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            oldHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            //await _leadHistoryRepo.UpdateAsync(, cancellationToken);
                        }
                        else
                        {
                            oldHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            // await _leadHistoryRepo.UpdateAsync(, cancellationToken);
                        }
                    }
                    else
                    {
                        newHistory = leadHistory;
                        //await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            oldHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);

                            // await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                        else
                        {
                            oldHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);

                            //await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest), cancellationToken);
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            oldHistory = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory);
                            //await _leadHistoryRepo.UpdateAsync(, cancellationToken);
                        }
                        else
                        {
                            newHistory = leadHistory;
                            // await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                        }
                    }
                }
                return (oldHistory, newHistory);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<bool> CreateLeadAssignmentsHistory(List<Domain.Entities.Lead> leads, LeadAssignmentType assignmentType, LeadAssignmentDto? assignmentDto = null, CancellationToken cancellationToken = default, bool isDuplicate = default)
        {
            List<LeadAssignment> leadAssignemnts = new();
            foreach (var lead in leads)
            {
                var leadAssignmentHistories = await _leadAssignmentRepo.ListAsync(new GetLeadAssignmentsByIdSpecs(lead.Id));
                if (leadAssignmentHistories == null)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                        LastModifiedBy = lead.LastModifiedBy,
                        CreatedBy = lead.LastModifiedBy
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.ToString();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    leadAssignemnts.Add(assignment);
                }
                else
                {
                    var leadLastAssignment = leadAssignmentHistories?.LastOrDefault();
                    if (leadLastAssignment?.AssignTo != lead?.AssignTo)
                    {
                        var assignment = new LeadAssignment()
                        {
                            AssignTo = lead.AssignTo,
                            AssignedFrom = lead.AssignedFrom,
                            Notes = lead.Notes,
                            LeadId = lead.Id,
                            UserId = lead.AssignTo,
                            LeadAssignmentType = assignmentType,
                            AssignmentDate = DateTime.UtcNow,
                            LastModifiedBy = lead.LastModifiedBy,
                            CreatedBy = lead.LastModifiedBy
                        };
                        if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignTo = lead.SecondaryUserId;
                        }
                        if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                        }
                        if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                        {
                            assignment.ProjectName = assignmentDto?.Projects?.ToString();
                            assignment.SourceName = assignmentDto?.LeadSource.ToString();
                        }
                        if (isDuplicate)
                        {
                            assignment.IsDuplicate = true;
                        }
                        leadAssignemnts.Add(assignment);
                    }
                    else if (lead?.SecondaryUserId != null && leadLastAssignment?.SecondaryAssignTo != lead.SecondaryUserId)
                    {
                        var assignment = new LeadAssignment()
                        {
                            AssignTo = lead.AssignTo,
                            AssignedFrom = lead.AssignedFrom,
                            Notes = lead.Notes,
                            LeadId = lead.Id,
                            UserId = lead.AssignTo,
                            LeadAssignmentType = assignmentType,
                            AssignmentDate = DateTime.UtcNow,
                            LastModifiedBy = lead.LastModifiedBy,
                            CreatedBy = lead.LastModifiedBy
                        };
                        if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignTo = lead.SecondaryUserId;
                        }
                        if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                        }
                        if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                        {
                            assignment.ProjectName = assignmentDto?.Projects?.ToString();
                            assignment.SourceName = assignmentDto?.LeadSource.ToString();
                        }
                        if (isDuplicate)
                        {
                            assignment.IsDuplicate = true;
                        }
                        leadAssignemnts.Add(assignment);
                    }
                }
            }
            await _leadAssignmentRepo.AddRangeAsync(leadAssignemnts, cancellationToken);
            return true;
        }

    }
}
