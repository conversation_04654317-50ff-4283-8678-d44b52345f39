﻿using Lrb.Application.Common.Interfaces;
using Lrb.Infrastructure.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Persistence.Repository.Interface
{
    public interface IUserRepository : ITransientService
    {
        Task<bool> UpdateUserRefreshToken(ApplicationUser user);
        Task<bool> UpdateUserLockAccount(bool isLocked, string userName, string tenantId);
    }
}
