﻿namespace Lrb.Application.GlobalSettings.Web
{
    public class GetGlobalSettingsSpec : Specification<Domain.Entities.GlobalSettings>
    {
        public GetGlobalSettingsSpec()
        {
            Query.Where(i => !i.IsDeleted);

        }
        public GetGlobalSettingsSpec(bool isDefault)
        {
            Query.Where(i => !i.IsDeleted);
        }

    }
    public class GetGlobalSettingsSpecV1 : Specification<Domain.Entities.GlobalSettings>
    {
        public GetGlobalSettingsSpecV1()
        {
            Query.Where(i => !i.IsDeleted);
              
        }

    }
}
