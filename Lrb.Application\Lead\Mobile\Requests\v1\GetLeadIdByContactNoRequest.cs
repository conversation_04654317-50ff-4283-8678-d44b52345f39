﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Domain.Entities;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile
{
    public class GetLeadIdByContactNoRequest : IRequest<Response<LeadContactDto>>
    {
        public string? ContactNo { get; set; }
        public string? CountryCode {  get; set; }
        public GetLeadIdByContactNoRequest(string contactno,string countryCode)
        {
            ContactNo = contactno;
            CountryCode = countryCode;
        }
    }
    public class GetLeadByContactNoRequestHandler : IRequestHandler<GetLeadIdByContactNoRequest, Response<LeadContactDto>>
    {
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;

        public GetLeadByContactNoRequestHandler(IReadRepository<Domain.Entities.Lead> leadRepo, ICurrentUser currentUser, IDapperRepository dapperRepository, IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo)
        {
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
            _globalSettingsRepo = globalSettingsRepo;
        }
        public async Task<Response<LeadContactDto>> Handle(GetLeadIdByContactNoRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.ContactNo)) { throw new Exception("Contact No field can not be empty."); }
            if (string.IsNullOrWhiteSpace(request?.CountryCode?.Trim()))
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);

                request.CountryCode = countries.FirstOrDefault().DefaultCallingCode;
            }
            if (string.IsNullOrEmpty(request.CountryCode)) { throw new Exception("Country Code field can not be empty."); }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsWithAdminAsync(userId, tenantId ?? string.Empty,viewAllLeads:null));
            string rawContactNo = request.ContactNo.Trim();
            var lead = (await _leadRepo.FirstOrDefaultAsync(new CheckLeadByContactNoSpec(request.ContactNo,request.CountryCode), cancellationToken));
            if (lead != null)
            {
                if(subIds.Value.Contains(lead.AssignTo) || subIds.Key)
                {
                    return new(new LeadContactDto() { Id = lead.Id, CanNavigate = true });
                }
                else
                {
                    return new(new LeadContactDto() { Id = lead.Id, CanNavigate = false });
                }
            }
            return new(null);
        }
    }
}