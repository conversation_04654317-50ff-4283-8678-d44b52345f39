﻿using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Entities.WhatsApp;
using Newtonsoft.Json;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web.Requests.WhatsApp
{
    public class AsputilityWhatsappRequest : List<AsputilityWhatsappLeadDto>, IRequest<Response<bool>>
    {
        public string? Tenant { get; set; }
    }
    public class AsputilityWhatsappRequestHandler : IRequestHandler<AsputilityWhatsappRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMasterLeadStatusRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<AsputilityWhatsappChats> _asputilityWhatsappChatsRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalsettingRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Project> _projectRepo;
        protected readonly INpgsqlRepository _npgsqlRepo;
        protected readonly IServiceBus _serviceBus;
        protected readonly IUserService _userService;
        protected readonly INotificationSenderService _notificationSenderService;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;

        public AsputilityWhatsappRequestHandler(IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatusRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<AsputilityWhatsappChats> asputilityWhatsappChatsRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalsettingRepo,
            IRepositoryWithEvents<Domain.Entities.Project> projectRepo,
            INpgsqlRepository npgsqlRepo,
            IServiceBus serviceBus,
            IUserService userService,
            INotificationSenderService notificationSenderService,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo)
        {
            _leadRepo = leadRepo;
            _customMasterLeadStatusRepo = customMasterLeadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _asputilityWhatsappChatsRepo = asputilityWhatsappChatsRepo;
            _globalsettingRepo = globalsettingRepo;
            _projectRepo = projectRepo;
            _npgsqlRepo = npgsqlRepo;
            _serviceBus = serviceBus;
            _userService = userService;
            _notificationSenderService = notificationSenderService;
            _duplicateInfoRepo = duplicateInfoRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _leadEnquiryRepo = leadEnquiryRepo;
            _userDetailsRepo = userDetailsRepo;
        }

        public async Task<Response<bool>> Handle(AsputilityWhatsappRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(request?.FirstOrDefault()?.ContactNo))
                {
                    return new(false);
                }
                var contactNo = request?.FirstOrDefault()?.ContactNo?.Substring(Math.Max(0, request?.FirstOrDefault()?.ContactNo?.Length - 10 ?? 0));
                var leads = await _leadRepo.ListAsync(new LeadByContactNoSpec(contactNo ?? "Invalid ContactNo"));
                if (leads?.Any() ?? false)
                {
                    foreach (var lead in leads)
                    {
                        await UpdateExistingLeadAsync(lead, request);
                    }
                    var chats = await _asputilityWhatsappChatsRepo.FirstOrDefaultAsync(new GetAsputilityWhatsappChatByUserIdSpec(request?.FirstOrDefault()?.UserId ?? string.Empty));
                    if (chats == null)
                    {
                        AsputilityWhatsappChats asputilityWhatsappChat = new();
                        asputilityWhatsappChat.ChatMessage = System.Text.Json.JsonSerializer.Serialize(request?.Adapt<List<AsputilityChatsDto>>());
                        asputilityWhatsappChat.UserId = request?.FirstOrDefault()?.UserId;
                        asputilityWhatsappChat.UserPhone = request?.FirstOrDefault()?.ContactNo;
                        await _asputilityWhatsappChatsRepo.AddAsync(asputilityWhatsappChat);
                    }
                }
                else
                {
                    await AddLeadAsync(request,cancellationToken);
                    var chats = await _asputilityWhatsappChatsRepo.FirstOrDefaultAsync(new GetAsputilityWhatsappChatByUserIdSpec(request?.FirstOrDefault()?.UserId ?? string.Empty));
                    foreach(var chat in request)
                    {
                        if (chat != null && !chats.ChatMessage.Contains(chat?.ChatId))
                        {
                            AsputilityWhatsappChats asputilityWhatsappChat = new();
                            asputilityWhatsappChat.ChatMessage = System.Text.Json.JsonSerializer.Serialize(request?.Adapt<List<AsputilityChatsDto>>());
                            asputilityWhatsappChat.UserId = request?.FirstOrDefault()?.UserId;
                            asputilityWhatsappChat.UserPhone = request?.FirstOrDefault()?.ContactNo;
                            await _asputilityWhatsappChatsRepo.AddAsync(asputilityWhatsappChat);
                        }
                    }
                }
                return new(true);
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<bool> AddLeadAsync(AsputilityWhatsappRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var notes = GetAdditionalNotes(request);
                if (!string.IsNullOrEmpty(notes))
                {
                    Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
                    var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);

                    var countryCode = countries?.FirstOrDefault()?.DefaultCallingCode;
                    Lrb.Domain.Entities.Lead newLead = new();
                    List<Lrb.Domain.Entities.LeadEnquiry> newLeadEnquiries = new() { new LeadEnquiry() { LeadSource = LeadSource.WhatsApp, IsPrimary = true } };
                    newLead.ContactNo = request.FirstOrDefault()?.ContactNo ?? string.Empty;
                    if (newLead.ContactNo.StartsWith("0"))
                    {
                        newLead.ContactNo = newLead.ContactNo.Substring(1);
                    }
                    if (!newLead.ContactNo.StartsWith("+") && (newLead.ContactNo.StartsWith("91") && (newLead.ContactNo.Length == 12)))
                    {
                        newLead.ContactNo = "+" + newLead.ContactNo;   
                    }
                    if (!newLead.ContactNo.StartsWith(countryCode))
                    {
                        newLead.ContactNo = countryCode + newLead.ContactNo;
                    }
                    newLead.Name = $"Asputility_whatsapp_{DateTime.Now.ToString()}";
                    newLead.Enquiries = newLeadEnquiries;
                    newLead.Notes = notes;
                    var projectName = GetProjectName(request?.FirstOrDefault()?.ChatMessage);
                    if (!string.IsNullOrEmpty(projectName))
                    {
                        newLead = await SetLeadProjectsAsync(newLead, projectName);
                    }
                    var customStatus = await _customMasterLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec());
                    newLead.CustomLeadStatus = customStatus ?? (await _customMasterLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                    await _leadRepo.AddAsync(newLead);
                    var leadDto = newLead.Adapt<ViewLeadDto>();
                    var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                    await _leadHistoryRepo.AddAsync(leadHsitory);
                }
            }
            catch (Exception)
            {
                return new();
            }
            return true;
        }

        public async Task<bool> UpdateExistingLeadAsync(Lrb.Domain.Entities.Lead lead, AsputilityWhatsappRequest request)
        {
            try
            {
                var notes = GetAdditionalNotes(request);
                var existingLeadHistory1 = await _leadHistoryRepo.ListAsync(new Lrb.Application.Lead.Web.Specs.LeadHistorySpec(lead.Id));
                var allnotes  = existingLeadHistory1.Select(i=>i.Notes).ToList();

                if (allnotes.All(i => !i.Values.Contains(notes)))
                {
                    if (lead.Notes != notes)
                    {
                        lead.Notes = notes;
                        await _leadRepo.UpdateAsync(lead);
                        var leadDto = lead.Adapt<ViewLeadDto>();
                        var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                        var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new Lrb.Application.Lead.Web.Specs.LeadHistorySpec(lead.Id));
                        if (existingLeadHistory != null)
                        {
                            await _leadHistoryRepo.UpdateAsync(LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHsitory));
                        }
                        else
                        {
                            await _leadHistoryRepo.AddAsync(leadHsitory);
                        }
                        //await SendNotificationAsync(request?.Tenant, lead);
                    }
                }
                return true;
                
            }
            catch (Exception)
            {
                return new();
            }
        }

        public string GetAdditionalNotes(AsputilityWhatsappRequest chats)
        {
            try
            {
                string? notes = string.Empty;
                int count = 1;
                chats?.ForEach(chat =>
                {
                    notes = notes + $"Chat_no {count}: {chat.ChatMessage}\n";
                    count++;
                });
                return notes;
            }
            catch (Exception)
            {
                return null;
            }
        }
        public string GetProjectName(string message)
        {
            try
            {
                string pattern = "(.*?)(?=\".*?\")(.*$)";
                Match match = Regex.Match(message, pattern);

                // If a match is found
                if (match.Success)
                {
                    // Get the captured groups
                    string beforeQuotedText = match.Groups[1].Value.Trim();
                    string afterQuotedText = match.Groups[2].Value.Trim();
                    return afterQuotedText.Replace("\"", "").Trim().Replace("\"", "");
                }
                return string.Empty;
            }
            catch (Exception)
            {
                return null;
            }
        }
        public async Task<Lrb.Domain.Entities.Lead> SetLeadProjectsAsync(Domain.Entities.Lead lead, string newProject, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new Lrb.Application.GlobalSettings.Web.GetGlobalSettingsSpec(), cancellationToken);
                var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);

                if (!string.IsNullOrWhiteSpace(newProject))
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.FirstOrDefaultAsync(new GetProjectsByNameSpec(newProject), cancellationToken));
                    if (existingProject != null)
                    {
                        projects.Add(existingProject);
                        var duplicateInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken))?.FirstOrDefault();
                        if (duplicateInfo != null && globalSettings != null && existingProject?.UserAssignment != null)
                        {
                            var assignedLead = await AddProjectAssignments(duplicateInfo, globalSettings, projects?.FirstOrDefault()?.UserAssignment, lead, projects);
                            if (assignedLead != null)
                            {
                                lead = assignedLead;
                            }
                        }
                    }
                    else
                    {
                        Lrb.Domain.Entities.Project project = new()
                        {
                            Name = newProject,
                            MonetaryInfo = new ProjectMonetaryInfo
                            {
                                Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        project = await _projectRepo.AddAsync(project, cancellationToken);
                        projects.Add(project);
                    }
                    if (lead.Projects == null && projects != null)
                    {
                        lead.Projects = projects;
                    }
                }
                else if ((lead?.Projects?.Any() ?? false) && newProject == null)
                {
                    lead.Projects = projects ?? null;
                }
                return lead;
            }
            catch (Exception)
            {
                return null;
            }
        }
        public async Task<bool> SendNotificationAsync(string tenant, Lrb.Domain.Entities.Lead lead)
        {
            try
            {
                if (lead.AssignTo != Guid.Empty)
                {
                    CancellationToken cancellationToken = CancellationToken.None;
                    var data = new SendNotificationDto() { @event = Lrb.Domain.Enums.Event.LeadInfoUpdate, Entity = lead, AssignTo = lead.AssignTo, UserName = null, CurrentUserId = lead.AssignTo, NoOfEntities = 1, TenantId = tenant ?? string.Empty };
                    var payload = new InputPayload(tenant ?? string.Empty, lead.AssignTo, "notification", data);
                    await _serviceBus.RunNotificationJobAsync(payload);
                    return true;
                }
                return false;
            }
            catch (Exception)
            {
                return new();
            }

        }

        public async Task<Lrb.Domain.Entities.Lead> AddProjectAssignments(DuplicateLeadFeatureInfo duplicateInfo, Lrb.Domain.Entities.GlobalSettings globalSettings, UserAssignment userAssignmentAndProject, Lrb.Domain.Entities.Lead lead, List<Lrb.Domain.Entities.Project> projects)
        {
            try 
            {
                if (userAssignmentAndProject.UserIds != null && userAssignmentAndProject.UserIds.Any())
                {
                    if (projects.FirstOrDefault()?.UserAssignment?.NextUserToBeAssigned != null && projects.FirstOrDefault()?.UserAssignment?.NextUserToBeAssigned != Guid.Empty)
                    {
                        lead.AssignTo = (Guid)(projects?.FirstOrDefault()?.UserAssignment?.NextUserToBeAssigned);
                        var currentIndex = userAssignmentAndProject.UserIds.IndexOf(lead.AssignTo);
                        var currentUser = lead.AssignTo;
                        if (currentIndex+1 == userAssignmentAndProject.UserIds.Count)
                        {
                            projects.FirstOrDefault().UserAssignment.LastAssignedUser = currentUser;
                            projects.FirstOrDefault().UserAssignment.NextUserToBeAssigned = Guid.Empty;
                        }
                        else
                        {
                            projects.FirstOrDefault().UserAssignment.LastAssignedUser = currentUser;
                            projects.FirstOrDefault().UserAssignment.NextUserToBeAssigned = userAssignmentAndProject.UserIds[currentIndex + 1];
                        }
                    }
                    else
                    {
                        var currentUser = userAssignmentAndProject?.UserIds.FirstOrDefault();
                        var currentIndex = userAssignmentAndProject.UserIds.IndexOf((Guid)currentUser);
                        projects.FirstOrDefault().UserAssignment.LastAssignedUser = currentUser;
                        projects.FirstOrDefault().UserAssignment.NextUserToBeAssigned = userAssignmentAndProject.UserIds[currentIndex + 1];
                        lead.AssignTo = (Guid)currentUser;
                    }
                    lead.Projects = projects;
                }
            
            return lead;
            }
            catch (Exception ex) { }
            return null;
        }
        //public async Task<bool> SendNotificationAsync(string tenant, Lrb.Domain.Entities.Lead lead, CancellationToken cancellationToken = default)
        //{
        //    if (lead?.AssignTo != Guid.Empty)
        //    {
        //        var assignedUser = await _userService.GetAsync(lead?.AssignTo.ToString() ?? string.Empty, cancellationToken);
        //        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Lrb.Domain.Enums.Event.LeadInfoUpdate, lead, lead?.AssignTo, assignedUser?.FirstName + " " + assignedUser?.LastName);
        //        return true;
        //    }
        //    return false;
        //}
    }
}
