﻿using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Project.Web.Specs;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class AssignProspectRequest : IRequest<Response<(int, int)>>
    {
        public List<Guid>? Ids { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<UserDetailsDto>? UserDetails { get; set; }
        public Guid? CurrentUserId { get; set; }
        public List<CustomProspectStatus>? Statuses { get; set; }
        public List<Domain.Entities.MasterData.MasterPropertyType>? PropertyTypes { get; set; }
        public List<MasterProspectSource>? Sources { get; set; }
        public Guid? TrackerId { get; set; }
        public Guid? ProspectSourceId { get; set; }
        public string? SubSource { get; set; }
        public List<string>? Projects { get; set; }
        public LeadAssignmentType AssignmentType { get; set; }
        public bool UpdateSource { get; set; }
        public bool UpdateProject { get; set; }
        public bool UpdateSubSource { get; set; }
    }

    public class AssignProspectRequestHanlder : IRequestHandler<AssignProspectRequest, Response<(int, int)>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> _propertyTypeRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.GlobalSettings> _globalSettingRepo;
        public AssignProspectRequestHanlder(
            IRepositoryWithEvents<Prospect> prospectRepo,
            ICurrentUser currentUser,
            IUserService userService,
            IRepositoryWithEvents<Lrb.Domain.Entities.CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.MasterData.MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<ProspectHistory> prospcetHistoryRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingRepo)
        {
            _prospectRepo = prospectRepo;
            _currentUser = currentUser;
            _userService = userService;
            _prospectHistoryRepo = prospcetHistoryRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _projectRepo = projectRepo;
            _globalSettingRepo = globalSettingRepo;
        }
        public async Task<Response<(int, int)>> Handle(AssignProspectRequest request, CancellationToken cancellationToken)
        {
            var updatedProspects = new List<Prospect>();
            var prospectHistories = new List<ProspectHistory>();
            int updatedCount = 0, notUpdatedCount = 0;

            var globalSetting = await _globalSettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            var prospects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.Ids), cancellationToken);
            if (!prospects.Any()) throw new NotFoundException("No Prospect Found By this Ids");

            var currentUserId = request.CurrentUserId ?? _currentUser.GetUserId();
            var users = request.UserDetails ?? await _userService.GetListAsync(cancellationToken);
            var statuses = request.Statuses ?? await _prospectStatusRepo.ListAsync();
            var propertyTypes = request.PropertyTypes ?? await _propertyTypeRepo.ListAsync();
            var sources = request.Sources ?? await _prospectSourceRepo.ListAsync();
            var assignedUsers = users.Where(u => request.UserIds.Contains(u.Id)).ToList();

            int userIndex = 0;
            foreach (var prospect in prospects)
            {
                var user = assignedUsers[userIndex];

                if (prospect.AssignTo != user.Id)
                {
                    var oldProspectDto = prospect.Adapt<ViewProspectDto>();
                    prospect.AssignedFrom = prospect.AssignTo;
                    prospect.AssignTo = user.Id;
                    prospect.LastModifiedBy = currentUserId;
                    prospect.AssignmentType = request.AssignmentType;

                    await SetProspectProjectAsync(prospect, request.Projects, prospect.Projects, globalSetting, cancellationToken);
                    await SetReassignedDataEnquiryAsync(prospect, request, cancellationToken);
                    updatedCount++;

                    var prospectDto = prospect.Adapt<ViewProspectDto>();
                    var oldDto = await ProspectHistoryHelper.SetUserViewForProspect(oldProspectDto, _userService, cancellationToken, currentUserId, users);
                    var newDto = await ProspectHistoryHelper.SetUserViewForProspect(prospectDto, _userService, cancellationToken, currentUserId, users);

                    var assignedTo = users.FirstOrDefault(u => u.Id == currentUserId);
                    var assignedToName = $"{assignedTo?.FirstName} {assignedTo?.LastName}".Trim();

                    switch (request.AssignmentType)
                    {
                        case LeadAssignmentType.WithoutHistory:
                        case LeadAssignmentType.WithoutHistoryWithNewStatus:
                            if (request.AssignmentType == LeadAssignmentType.WithoutHistoryWithNewStatus)
                            {
                                var status = await _prospectStatusRepo.FirstOrDefaultAsync(new GetDefaultProspectStatusSpecs(), cancellationToken);
                                prospect.Status = status;
                                prospect.ScheduleDate = null;
                            }

                            updatedProspects.Add(prospect);
                            await UpdateAndCreateProspectHistoryAsync(prospect, user.Id, assignedToName);
                            var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(newDto, oldDto, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken, oldDto.AssignTo);
                            prospectHistories.AddRange(histories);
                            break;

                        case LeadAssignmentType.WithHistory:
                            histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(newDto, oldDto, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                            prospectHistories.AddRange(histories);
                            break;
                    }
                }
                else
                {
                    notUpdatedCount++;
                }

                userIndex = (userIndex + 1) % assignedUsers.Count;
            }

            await _prospectRepo.UpdateRangeAsync(updatedProspects);
            if (prospectHistories.Any())
                await _prospectHistoryRepo.AddRangeAsync(prospectHistories);

            return new() { Data = (updatedCount, notUpdatedCount) };
        }
        public async Task<ProspectHistory?> UpdateAndCreateProspectHistoryAsync(Domain.Entities.Prospect prospect, Guid? user, string? username)
        {
            var existingHistories = await _prospectHistoryRepo.ListAsync(
                new GetProspectV1HistoryByProspectIdSpecs(prospect.Id));

            if (existingHistories == null || !existingHistories.Any())
                return null;
            var newHistories = existingHistories.Select(history => new ProspectHistory
            {
                ProspectId = history.ProspectId,
                FieldName = history.FieldName,
                FieldType = history.FieldType,
                RelatedEntityId = history.RelatedEntityId,
                RelatedEntityName = history.RelatedEntityName,
                RelatedEntityChain = history.RelatedEntityChain,
                OldValue = history.OldValue,
                NewValue = history.NewValue,
                ModifiedOn = history.ModifiedOn,
                ModifiedBy = username ?? string.Empty,
                LastModifiedById = history.LastModifiedById,
                GroupKey = history.GroupKey,
                Version = history.Version + 2,
                UserId = user,
            }).ToList();
            await _prospectHistoryRepo.AddRangeAsync(newHistories);
            return newHistories.LastOrDefault();
        }

        protected async Task SetProspectProjectAsync(Lrb.Domain.Entities.Prospect prospect, List<string>? projectList, IList<Domain.Entities.Project> oldProjects, Domain.Entities.GlobalSettings? globalSettings, CancellationToken cancellationToken)
        {
            List<Lrb.Domain.Entities.Project> tempProject = new();
            projectList = (projectList?.Any() ?? false) ? projectList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
            var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSettings?.CountriesInfo);
            if (projectList?.Any() ?? false)
            {
                foreach (var newProject in projectList)
                {
                    Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.ListAsync(new GetProjectByNameSpecs(newProject), cancellationToken)).FirstOrDefault();
                    if (existingProject != null)
                    {
                        tempProject.Add(existingProject);
                    }
                    else
                    {
                        Domain.Entities.Project projects = new()
                        {
                            Name = newProject,
                            MonetaryInfo = new ProjectMonetaryInfo
                            {
                                Currency = countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                            }
                        };
                        projects = await _projectRepo.AddAsync(projects, cancellationToken);
                        tempProject.Add(projects);
                    }
                }
            }
            prospect.Projects = tempProject?.Any() ?? false ? tempProject : oldProjects;
        }
        protected async Task SetReassignedDataEnquiryAsync(Domain.Entities.Prospect prospect, AssignProspectRequest request, CancellationToken cancellationToken = default, ILeadRepositoryAsync? leadRepositoryAsync = null)
        {
            try
            {
                MasterProspectSource? source = null;
                if (prospect.Enquiries != null && prospect.Enquiries.Any())
                {
                    var primaryEnquiry = prospect.Enquiries.FirstOrDefault(e => e.IsPrimary);
                    source = await _prospectSourceRepo.GetByIdAsync(request.ProspectSourceId, cancellationToken);
                    if (primaryEnquiry != null)
                    {
                        if (request.UpdateSubSource && !string.IsNullOrEmpty(request.SubSource))
                        {
                            primaryEnquiry.SubSource = request.SubSource;
                        }
                        if (request.UpdateSource)
                        {
                            primaryEnquiry.Source = source;
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
    }
}
