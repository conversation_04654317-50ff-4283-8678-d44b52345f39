﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;
using Lrb.Application.TimeZone.Dto;
using Lrb.Application.TimeZone.Requests;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using NodaTime;
using NodaTime.Extensions;
using NodaTime.TimeZones;
using System;
using System.Data;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using TimeZoneConverter;

namespace Lrb.Application.UserDetails.Web
{
    public class CreateBulkUsersRequest : IRequest<Response<bool>>
    {
        public IFormFile FormFile;
        public string Origin { get; set; }
        public CreateBulkUsersRequest(IFormFile formFile, string originRequest)
        {
            FormFile = formFile;
            Origin = originRequest;
        }
    }
    public class CreateBulkUsersRequestHandler : IRequestHandler<CreateBulkUsersRequest, Response<bool>>
    {
        public const string DefaultPassword = "123Pa$$word!";
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepository;
        private readonly IRepositoryWithEvents<Department> _departmentRepo;
        private readonly IRepositoryWithEvents<Designation> _designationRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettings;
        private readonly ICurrentUser _currentUser;

        public CreateBulkUsersRequestHandler(IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepository, IRepositoryWithEvents<Department> departmentRepo, IRepositoryWithEvents<Designation> designationRepo
            , IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettings, ICurrentUser currentUser)
        {
            _userService = userService;
            _userDetailsRepository = userDetailsRepository;
            _departmentRepo = departmentRepo;
            _designationRepo = designationRepo;
            _globalSettings = globalSettings;
            _currentUser = currentUser;
        }

        public async Task<Response<bool>> Handle(CreateBulkUsersRequest file, CancellationToken cancellationToken)
        {
            var dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(file.FormFile);
            var globalSettings = await _globalSettings.FirstOrDefaultAsync(new GlobalSettings.Web.GetGlobalSettingsSpec());
            var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);


            int totalRows = dataTable.Rows.Count;
            for (int i = totalRows - 1; i >= 0; i--)
            {
                var row = dataTable.Rows[i];
                if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                {
                    row.Delete();
                }
            }
            if (dataTable.Rows.Count <= 0)
            {
                return new(false, "Excel sheet is empty. Please fill some data in the excel sheet template.");
            }
            List<DuplicateUsersDto> duplicateUsersDto = new List<DuplicateUsersDto>();

            List<CreateUserRequest> users = new List<CreateUserRequest>();
            foreach (DataRow row in dataTable.Rows)
            {
                DuplicateUsersDto duplicateUsers = new DuplicateUsersDto();
                CreateUserRequest userDetails = new CreateUserRequest();
                #region Name Validation
                userDetails.FirstName = row["FirstName"].ToString();
                if (string.IsNullOrWhiteSpace(userDetails.FirstName))
                {
                    return new(false, "FirstName can not be null!");
                }
                userDetails.LastName = row["LastName"].ToString();
                if (string.IsNullOrWhiteSpace(userDetails.LastName))
                {
                    return new(false, "LastName can not be null!");
                }
                userDetails.UserName = row["UserName"].ToString();
                if (string.IsNullOrWhiteSpace(userDetails?.UserName?.Trim()))
                {
                    return new(false, "UserName can not be null!");
                }
                if (userDetails.UserName.Contains(" "))
                {
                    return new(false, "UserName can not contain a space character! " + userDetails.UserName);
                }
                if (users.Select(i => i.UserName).Contains(userDetails.UserName.Trim()))
                {
                    return new(false, "UserName already exists in excel sheet! " + userDetails.UserName);
                }
                if (await _userService.ExistsWithNameAsync(userDetails.UserName))
                {
                    duplicateUsers.UserName = userDetails.UserName;
                }
                #endregion
                #region PhoneNumber Validation
                string phoneNumber = row["PhoneNumber"].ToString() ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(phoneNumber))
                {
                    userDetails.PhoneNumber = ListingSitesHelper.ConcatenatePhoneNumberV2(countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91", phoneNumber, globalSettings, string.Empty);
                }
                else
                {
                    return new(false, "PhoneNumber can not be null!");
                }

                if (users.Select(i => i.PhoneNumber).Contains(userDetails.PhoneNumber.Trim()))
                {
                    return new(false, "PhoneNumber already exists in excel sheet! " + userDetails.PhoneNumber);
                }
                if (await _userService.ExistsWithPhoneNumberAsync(userDetails.PhoneNumber.Trim()))
                {
                    duplicateUsers.PhoneNumber = userDetails.PhoneNumber;
                }
                #endregion
                #region Email Validation
                userDetails.Email = row["Email"].ToString();
                if (!string.IsNullOrWhiteSpace(userDetails.Email.Trim()))
                {
                    if (Regex.IsMatch(userDetails.Email, RegexPatterns.EmailPattern))
                    {
                        userDetails.Email = row["Email"].ToString();
                    }
                    else
                    {
                        return new(false, "Email can not be null! " + userDetails.Email);
                    }
                }
                if (await _userService.ExistsWithEmailAsync(userDetails.Email.Trim()))
                {
                    duplicateUsers.Email = userDetails.Email;
                }
                if (users.Select(i => i.Email).Contains(userDetails.Email.Trim()))
                {
                    return new(false, "Email already exist in excel sheet! " + userDetails.Email);
                }
                #endregion

                userDetails.Address = row["Address"].ToString();
                if (string.IsNullOrWhiteSpace(row["BloodGroupType"].ToString()))
                {
                    userDetails.BloodGroup = BloodGroupType.None;
                }
                else
                {
                    userDetails.BloodGroup = Enum.Parse<BloodGroupType>(row["BloodGroupType"].ToString());
                }

                if (string.IsNullOrWhiteSpace(row["Gender"].ToString()))
                {
                    userDetails.Gender = Gender.NotMentioned;
                }
                else
                {
                    userDetails.Gender = Enum.Parse<Gender>(row["Gender"].ToString());
                }

                if (!string.IsNullOrWhiteSpace(row["Department"].ToString()))
                {
                    var departmentList = await _departmentRepo.ListAsync(new GetAllDepartmentsSpec());
                    string departments = row["Department"].ToString();
                    var department = departmentList.FirstOrDefault(u => u.Name.ToLower() == departments.ToLower().Trim().Replace(" ", ""));


                    if (department != null)
                    {
                        userDetails.DepartmentId = department.Id;
                    }
                    else
                    {
                        Lrb.Domain.Entities.Department departmentv1 = new()
                        {
                            Name = departments,

                        };
                        await _departmentRepo.AddAsync(departmentv1, cancellationToken);
                    }
                }
                if (!string.IsNullOrWhiteSpace(row["Designation"].ToString()))
                {
                    List<Lrb.Application.Identity.Users.UserDetailsDto> usersList = new(await _userService.GetListAsync(cancellationToken));

                    var designationList = await _designationRepo.ListAsync(new GetAllDesignationSpec());
                    string designatons = row["Designation"].ToString();
                    var designation = designationList.FirstOrDefault(u => u.Name.ToLower() == designatons.ToLower().Trim().Replace(" ", ""));

                    var designationsName = designationList.Select(i => i.Name.ToLower()).ToList();
                    if (designationsName.Contains(designatons.ToLower().Trim().Replace(" ", "")))
                    {
                        userDetails.DesignationId = designation.Id;
                    }
                    else
                    {
                        Designation designationsv1 = new()
                        {
                            Name = designatons,

                        };
                        await _designationRepo.AddAsync(designationsv1, cancellationToken);
                    }
                }
                if (!string.IsNullOrWhiteSpace(row["ReportTo"].ToString()))
                {
                    List<Lrb.Application.Identity.Users.UserDetailsDto> usersList = new(await _userService.GetListAsync(cancellationToken));
                    var username = usersList.Select(i => i.UserName.ToLower()).ToList();
                    string user = row["ReportTo"].ToString();
                    var assignToUser = usersList.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", ""));

                    if (assignToUser != null)
                    {
                        userDetails.ReportsTo = assignToUser.Id;
                    }
                    else
                    {
                        userDetails.ReportsTo = _currentUser.GetUserId();

                    }
                }
                if (!string.IsNullOrWhiteSpace(row["GeneralManager"].ToString()))
                {
                    List<Identity.Users.UserDetailsDto> usersList = new(await _userService.GetListAsync(cancellationToken));
                    var username = usersList.Select(i => i.UserName.ToLower()).ToList();
                    string user = row["GeneralManager"].ToString();
                    var assignToUser = usersList.FirstOrDefault(u => u.UserName.ToLower() == user.ToLower().Trim().Replace(" ", ""));


                    if (username.Contains(user.ToLower().Trim().Replace(" ", "")))
                    {
                        userDetails.GeneralManager = assignToUser.Id;
                    }
                    else
                    {
                        userDetails.ReportsTo = _currentUser.GetUserId();

                    }
                }

                userDetails.Description = row["Description"].ToString();
                users.Add(userDetails);
                if (duplicateUsers.UserName != null || duplicateUsers.Email != null || duplicateUsers.PhoneNumber != null)
                {
                    duplicateUsersDto.Add(duplicateUsers);
                }
            }
        
            GetAllDuplicateItemsModel duplicates = new();
            if (duplicateUsersDto.Any(i => i != null))
            {
                List<DuplicateUsersDto> duplicateItems = new();
                duplicateUsersDto.ToList().ForEach(i => duplicateItems.Add(new DuplicateUsersDto(i.UserName, i.PhoneNumber, i.Email, DuplicateItemType.User)));
                duplicates.DuplicateItems.AddRange(duplicateItems);
                duplicates.Count = duplicateItems.Count;
                duplicates.RequestedItemCount = dataTable.Rows.Count;
            }
            if (duplicates.DuplicateItems.Any())
            {
                return new Response<bool>(true, JsonConvert.SerializeObject(duplicates));
            }
            var countryCode = countries?.FirstOrDefault()?.Code;
            var timeZoneDto = GetTimeZoneForUsers(countryCode ?? "IN");
            foreach (var user in users)
            {
                var department = await _departmentRepo?.GetByIdAsync(user.DepartmentId);
                var designation = await _designationRepo?.GetByIdAsync(user.DesignationId);
                user.Password = DefaultPassword;
                user.ConfirmPassword = DefaultPassword;
                var res = await _userService.CreateAsync(user, file.Origin);
                Domain.Entities.UserDetails userDetails = user.Adapt<Domain.Entities.UserDetails>();
                userDetails.Designation = designation;
                userDetails.Department = department;
                userDetails.CurrentAddress = user.Address;
                userDetails.IsAutomationEnabled = true;
                userDetails.UserId = Guid.Parse(res.userId);
                userDetails.TimeZoneInfo = JsonConvert.SerializeObject(timeZoneDto);
                await _userDetailsRepository.AddAsync(userDetails, cancellationToken);
            }
            return new(true, $"Default password for {users.Count} created users : " + DefaultPassword);
        }
        static CreateTimeZoneDto GetTimeZoneForUsers(string countryCode)
        {
            CreateTimeZoneDto timeZoneDto = new CreateTimeZoneDto();
            var locations = TzdbDateTimeZoneSource.Default.ZoneLocations;
            var ianaZoneId = locations?.FirstOrDefault(i => i.CountryCode == countryCode)?.ZoneId;
            var windowsZoneId = DateTimeZoneProviders.Tzdb.GetZoneOrNull(ianaZoneId)?.Id;

            if (!string.IsNullOrWhiteSpace(windowsZoneId))
            {
                try
                {
                    TimeZoneInfo windowsZone = TimeZoneInfo.FindSystemTimeZoneById(windowsZoneId);
                    string displayName = windowsZone.DisplayName;
                    TimeSpan baseUtcOffset = windowsZone.BaseUtcOffset;
                    timeZoneDto.TimeZoneDisplay = displayName;
                    timeZoneDto.TimeZoneName = windowsZoneId;
                    timeZoneDto.TimeZoneId = ianaZoneId;
                    timeZoneDto.BaseUTcOffset = baseUtcOffset;
                }
                catch (TimeZoneNotFoundException)
                {
                    Console.WriteLine($"Windows timezone not found for {ianaZoneId}");                    
                }
            }
            return timeZoneDto;

        }
    }
}

