namespace Lrb.Application.Identity.Tokens;

public record TokenResponse(string Token, string RefreshToken, DateTime RefreshTokenExpiryTime);
public record CognitoTokenResponse(string IdToken, string AccessToken, string RefreshToken, string SessionId);


#region Account Locked Info
public class LockoutInfo
{
    public string? Username { get; set; }
    public DateTime LockoutExpiry { get; set; }
    public DateTime LockedAt { get; set; }
}

public class AccountLockoutInfo
{
    public string? Username { get; set; }
    public DateTime LockoutExpiry { get; set; }
    public TimeSpan RemainingTime { get; set; }
    public DateTime LockedAt { get; set; }
}
#endregion