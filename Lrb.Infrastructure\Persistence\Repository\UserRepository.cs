﻿using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.Persistence;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.Persistence.Repository.Interface;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;

namespace Lrb.Infrastructure.Persistence.Repository
{
    public class UserRepository : IUserRepository
    {
        private readonly DatabaseSettings _settings;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly ITenantInfo? _tenantInfo;

        public UserRepository(IOptions<DatabaseSettings> options, ITenantInfo tenantInfo, ILeadRepositoryAsync leadRepositoryAsync)
        {
            _settings = options.Value;
            _tenantInfo = tenantInfo;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<bool> UpdateUserRefreshToken(ApplicationUser user)
        {
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString);
            try
            {
                await conn.OpenAsync();
                var query = $"UPDATE \"Identity\".\"Users\" Set  \"RefreshToken\"= @refreshToken , \"RefreshTokenExpiryTime\" = @expiryTime where \"Id\" = @id";
                var command = new NpgsqlCommand(query, conn);
                command.Parameters.AddWithValue("@refreshToken", user.RefreshToken);
                command.Parameters.AddWithValue("@expiryTime", user.RefreshTokenExpiryTime);
                command.Parameters.AddWithValue("@id", user.Id);
                //command.Parameters.AddWithValue("@dateTime", DateTime.UtcNow);
                int affectedRows = command.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "UserRepository -> UpdateUserRefreshToken()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }

        public async Task<bool> UpdateUserLockAccount(bool isLocked, string userName, string tenantId)
        {
            var conn = new NpgsqlConnection(string.IsNullOrEmpty(_tenantInfo?.ConnectionString) ? _settings.ConnectionString : _tenantInfo.ConnectionString);
            try
            {
                var qyery = $"UPDATE \"Identity\".\"Users\" SET \"IsLocked\" = {isLocked} WHERE \"TenantId\" = '{tenantId}' AND \"UserName\" = '{userName}'";
                await conn.OpenAsync();
                var res = await conn.ExecuteAsync(qyery);
                return res > 0 ? true : false;
            }
            catch(Exception ex)
            {
                return false;
            }
            finally
            {
                await conn.CloseAsync();
            }
        }
    }
}
