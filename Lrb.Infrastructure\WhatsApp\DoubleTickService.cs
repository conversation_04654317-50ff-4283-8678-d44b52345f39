﻿using Amazon.Pinpoint.Model;
using DocumentFormat.OpenXml.Presentation;
using Lrb.Application.Common.Identity;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.WhatsApp.DoubleTick.Dtos;
using Lrb.Application.Common.WhatsApp.DoubleTick.Services;
using Lrb.Application.Common.WhatsApp.NoApp;
using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Enums;
using Mapster;
using Newtonsoft.Json;
using PhoneNumbers;
using RestSharp;
using Serilog;
using System.Linq;

namespace Lrb.Infrastructure.WhatsApp
{
    public class DoubleTickService : IDoubleTickServices
    {
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        public DoubleTickService(ILogger logger, IRepositoryWithEvents<GlobalSettings> globalSettingsRepo)
        {
            _logger = logger;
            _globalSettingsRepo = globalSettingsRepo;
        }
        public async Task<RestResponse> SendTemplate(BaseWhatsAppTemplateWithLeadIdDto baseTemplateDto, WhatsAppConfigurationByServiceProvider waConfiguration, WhatsAppHeaderTypes whatsAppHeaderTypes, WhatsAppAPIInfo? whatsAppApiInfos, WhatsAppTemplateInfoDto? WhatsAppTemplateInfoDto = null)
        {
            if (waConfiguration == null)
            {
                _logger.Information($"DoubleTickService -> SendTemplate -> WhatsAppApiInfo Not Found!");
                throw new NotFoundException($"DoubleTickService -> SendTemplate -> WhatsAppApiInfo Not Found!");
            }
            var client = new RestClient(waConfiguration.BaseURL ?? string.Empty);
            var request = new RestRequest();
            request.AddHeader("Authorization", waConfiguration.Authorization ?? string.Empty);
            request.AddHeader("accept", "application/json");
            request.AddHeader("content-type", "application/json");
            var template = await GetTemplatebyType(baseTemplateDto, whatsAppHeaderTypes, WhatsAppTemplateInfoDto);
            request.AddJsonBody(template);
            //request.AddBody(template);
            RestResponse response = new();
            try
            {
                response = await client.PostAsync(request);
            }
            catch (Exception ex) 
            {
                _logger.Information($"DoubleTickService -> SendTemplate() -> Exception : {JsonConvert.SerializeObject(ex)}");
                return new()
                {
                    IsSuccessStatusCode = false,
                };
            }
            return response;
        }

        private async Task<string> GetTemplatebyType(BaseWhatsAppTemplateWithLeadIdDto dto, WhatsAppHeaderTypes whatsAppHeaderTypes, WhatsAppTemplateInfoDto whatsAppTemplateInfo)
        {
            //Replacing first value in the body/header values with the lead name
            if (dto.ShouldAddLeadNameInBody)
            {
                if (dto.Template != null && dto.LeadName != null && dto.Template?.BodyValues?.Count == 0)
                {
                    dto.Template.BodyValues = dto.Template?.BodyValues?.Prepend(dto.LeadName).ToList() ?? dto.Template?.BodyValues;
                }
            }

            if (dto.ShouldAddLeadNameInHeader)
            {
                if (dto.Template != null && dto.LeadName != null)
                {
                    dto.Template.HeaderValues = dto.Template?.HeaderValues?.Prepend(dto.LeadName).ToList() ?? dto.Template?.HeaderValues;
                }
            }

            if (whatsAppTemplateInfo.BodyValuesCount > (dto.Template?.BodyValues?.Count ?? 0))
            {
                if (dto.Template != null && dto.Template.BodyValues == null)
                {
                    dto.Template.BodyValues = whatsAppTemplateInfo?.DefaultBodyValues?.ToList() ?? new();
                }
                else if (dto.Template != null && dto.Template.BodyValues != null)
                {
                    int numberOfValuesToTake = whatsAppTemplateInfo.BodyValuesCount - dto.Template.BodyValues.Count();
                    dto.Template.BodyValues.AddRange(whatsAppTemplateInfo?.DefaultBodyValues?.ToList().TakeLast(numberOfValuesToTake) ?? new List<string>());
                }
            }

            // Header Component
            DoubleTickHeaderComponent? header = null;

            if (whatsAppHeaderTypes != WhatsAppHeaderTypes.None)
            {
                header = await GetHeaderComponentByTypeAsync(dto, whatsAppHeaderTypes, whatsAppTemplateInfo);
            }
            

            // Body Payload
            DoubleTickBodyComponet body = new()
            {
                placeholders = dto.Template?.BodyValues?.Take(whatsAppTemplateInfo.BodyValuesCount).ToList() ?? new(),
            };

            //Adding Button Component
            var buttons = await GetButtonComponent(whatsAppTemplateInfo);

            //Template Data Payload
            DobleTickTemplateInfoDto dobleTickTemplateInfoDto = new()
            {
                header = header,
                body = body,
                buttons = buttons,
            };

            // Content Payload
            DoubleTickContentDto content = new()
            {
                language = dto.Template?.LanguageCode ?? whatsAppTemplateInfo?.LanguageCode.ToString(),
                templateData = dobleTickTemplateInfoDto,
                templateName = dto.Template?.Name
            };

            //Base Template Dto

            var contactNo = await ValidateContactNo(dto.FullPhoneNumber ?? string.Empty);

            DoubleTickTemplateDto doubleTickTemplateDto = new()
            {
                content = content,
                to = contactNo,
                from = dto.FromNumber,
            };
            List<DoubleTickTemplateDto> messages = new();
            messages.Add(doubleTickTemplateDto);

            DoubleTickMainPayload payload = new()
            {
                messages = messages
            };
            var jsonPayload = JsonConvert.SerializeObject(payload);
            return jsonPayload;
        }

        private async Task<DoubleTickHeaderComponent> GetHeaderComponentByTypeAsync(BaseWhatsAppTemplateWithLeadIdDto dto, WhatsAppHeaderTypes whatsAppHeaderTypes, WhatsAppTemplateInfoDto whatsAppTemplateInfo)
        {
            DoubleTickHeaderComponent header = new();

            switch(whatsAppHeaderTypes)
            {
                case WhatsAppHeaderTypes.None:
                    header.type = "none";
                    header.placeholder = string.Empty;
                    header.mediaUrl = string.Empty;
                    header.filename = dto.Template?.FileName ?? string.Empty;
                    header.latitude = string.Empty; 
                    header.longitude = string.Empty;
                    break;
                case WhatsAppHeaderTypes.Text:
                    header.type = "TEXT";
                    header.placeholder = dto.Template?.HeaderValues?.FirstOrDefault() ?? string.Empty;
                    header.mediaUrl = string.Empty;
                    header.filename = dto.Template?.FileName ?? string.Empty;
                    header.latitude = string.Empty;
                    header.longitude = string.Empty;
                    break;
                case WhatsAppHeaderTypes.Image:
                    header.type = "IMAGE";
                    header.placeholder = dto.Template?.HeaderValues?.FirstOrDefault();
                    header.mediaUrl = whatsAppTemplateInfo.MediaUrl?.ToString() ?? string.Empty;
                    header.filename = dto.Template?.FileName ?? string.Empty;
                    header.latitude = string.Empty;
                    header.longitude = string.Empty;
                    break;
                case WhatsAppHeaderTypes.Video:
                    header.type = "VIDEO";
                    header.placeholder = dto.Template?.HeaderValues?.FirstOrDefault();
                    header.mediaUrl = whatsAppTemplateInfo.MediaUrl?.ToString() ?? string.Empty;
                    header.filename = dto.Template?.FileName ?? string.Empty;
                    header.latitude = string.Empty;
                    header.longitude = string.Empty;
                    break;
                case WhatsAppHeaderTypes.Document:
                    header.type = "DOCUMENT";
                    header.placeholder = string.Empty;
                    header.mediaUrl = whatsAppTemplateInfo.MediaUrl?.ToString() ?? string.Empty;
                    header.filename = dto.Template?.FileName ?? string.Empty;
                    header.latitude = string.Empty;
                    header.longitude = string.Empty;
                    break;
            }

            return header;
        }

        private async Task<List<DoubleTickButtonComponent>> GetButtonComponent(WhatsAppTemplateInfoDto whatsAppTemplateInfo)
        {
            List<DoubleTickButtonComponent> buttonsToAdd = new();
            if (whatsAppTemplateInfo.ButtonValuesCount > 0 && (whatsAppTemplateInfo.WhatsAppTemplateButtonsInfos?.Any() ?? false) && whatsAppTemplateInfo.ShouldSendWithFlow)
            {
                var buttons = whatsAppTemplateInfo.WhatsAppTemplateButtonsInfos.OrderBy(i => i.OrderRank).ToList();
                
                foreach (var button in buttons)
                {
                    DoubleTickButtonComponent buttonComponent1 = new()
                    {
                        type = button.Type.ToString(),
                        parameters = button.FlowId
                    };
                    buttonsToAdd.Add(buttonComponent1);
                }
                return buttonsToAdd;
            }
            else
            {
                DoubleTickButtonComponent buttonComponent = new()
                {
                    type = "URL",
                    parameters = null
                };
                buttonsToAdd.Add(buttonComponent);
                return buttonsToAdd;
            }
        }

        private async Task<string> ValidateContactNo(string contactNo)
        {
            if (!string.IsNullOrEmpty(contactNo))
            {
                try
                {
                    string mobileNumber = string.Empty;
                    var globalSetting = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec());
                    var countries = JsonConvert.DeserializeObject<List<CountryInfoDto>>(globalSetting?.CountriesInfo);

                    if (globalSetting != null) 
                    {
                        PhoneNumberUtil numberUtil = PhoneNumberUtil.GetInstance();
                        var defaultRegion = countries?.FirstOrDefault()?.Code;
                        var defaultCallingCode = countries?.FirstOrDefault()?.DefaultCallingCode;
                        PhoneNumber number = numberUtil.Parse(contactNo, defaultRegion);
                        var countryCode = number.CountryCode.ToString();
                        if(contactNo.StartsWith(defaultCallingCode ?? string.Empty))
                        {
                            return contactNo;
                        }
                        else if(contactNo.StartsWith(countryCode))
                        {
                            return $"+{contactNo}";
                        }
                        return $"+{countryCode}{contactNo}";
                    }
                    return mobileNumber;
                }
                catch
                {

                }
            }
            return string.Empty;
        }
    }
}
