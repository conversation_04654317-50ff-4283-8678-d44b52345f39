﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.GoogleAuth;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.TempProject.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Text;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web
{
    public class CreateLeadFromPubSubMessageRequest : IRequest<Response<bool>>
    {
        public Root? PubSubMessage { get; set; }
        public bool IsMobileClient { get; set; }
    }
    public class CreateLeadFromPubSubMessageRequestHandler : IRequestHandler<CreateLeadFromPubSubMessageRequest, Response<bool>>
    {
        IRepositoryWithEvents<GmailIntegrationData> _gmailIntegrationDataRepositoryAsync;
        IRepositoryWithEvents<IntegrationLeadInfo> _leadInfoRepositoryAsync;
        private readonly IMobileGoogleOAuth2Service _mobileGoogleOAuth2Service;
        private readonly IWebGoogleOAuth2Service _webGoogleOAuth2Service;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IGmailService _gmailService;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILogger _logger;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;
        private readonly IMediator _mediator;

        public CreateLeadFromPubSubMessageRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<IntegrationLeadInfo> leadInfoRepositoryAsync,
            IRepositoryWithEvents<GmailIntegrationData> gmailIntegrationDataRepositoryAsync,
            IMobileGoogleOAuth2Service mobileGoogleOAuth2Service,
            IWebGoogleOAuth2Service webGoogleOAuth2Service,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IGmailService gmailService,
            INpgsqlRepository npgsqlRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ILogger logger,
            INotificationSenderService notificationSenderService,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService,
            IMediator mediator
            )
        {
            _gmailIntegrationDataRepositoryAsync = gmailIntegrationDataRepositoryAsync;
            _leadInfoRepositoryAsync = leadInfoRepositoryAsync;
            _mobileGoogleOAuth2Service = mobileGoogleOAuth2Service;
            _webGoogleOAuth2Service = webGoogleOAuth2Service;
            //_leadStatusRepo = leadStatusRepo;
            _leadRepo = leadRepo;
            _addressRepo = addressRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _gmailService = gmailService;
            _integrationRepo = integrationRepo;
            _integrationAccRepo = integrationAccRepo;
            _npgsqlRepo = npgsqlRepo;
            _projectRepo = projectRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _logger = logger;
            _notificationSenderService = notificationSenderService;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _leadRotationService = leadRotationService;
            _leadAssignmentRepo = leadAssignmentRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
            _mediator = mediator;
        }

        public async Task<Response<bool>> Handle(CreateLeadFromPubSubMessageRequest request, CancellationToken cancellationToken)
        {
            try
            {
                bool returnValue = true;
                byte[] data = Convert.FromBase64String(request.PubSubMessage.Message.data);
                string decodedString = Encoding.UTF8.GetString(data);
                HistoryData leadHistoryData = JsonConvert.DeserializeObject<HistoryData>(decodedString);

                if (string.IsNullOrEmpty(leadHistoryData?.EmailAddress))
                {
                    throw new Exception("The message does not contain any email address.");
                }
                GmailIntegrationData? storedGmaildata = (await _gmailIntegrationDataRepositoryAsync.ListAsync(new GmailDataByGmailSpec(leadHistoryData.EmailAddress)))?.FirstOrDefault();

                if (storedGmaildata != null && storedGmaildata?.HistoryId?.ToLowerInvariant() != leadHistoryData?.HistoryId?.ToLowerInvariant())
                {
                    TokenClass tokens = null;
                    if (request.IsMobileClient || !string.IsNullOrWhiteSpace(storedGmaildata.RefreshTokenMobile))
                    {
                        tokens = await _mobileGoogleOAuth2Service.GetAuthUsingRefreshToken(storedGmaildata.RefreshTokenMobile);
                    }
                    else
                    {
                        tokens = await _webGoogleOAuth2Service.GetAuthUsingRefreshToken(storedGmaildata.RefreshTokenWeb);
                    }
                    RootMessage gmailData = await _gmailService.GetEmailData(tokens.AccessToken, storedGmaildata.Email, storedGmaildata.HistoryId);
                    storedGmaildata.HistoryId = leadHistoryData.HistoryId;
                    if (tokens?.RefreshToken != null)
                    {
                        if (request.IsMobileClient || !string.IsNullOrWhiteSpace(storedGmaildata.RefreshTokenMobile))
                        {
                            storedGmaildata.RefreshTokenMobile = tokens.RefreshToken;
                        }
                        else
                        {
                            storedGmaildata.RefreshTokenWeb = tokens.RefreshToken;
                        }
                    }
                    await _gmailIntegrationDataRepositoryAsync.UpdateAsync(storedGmaildata);
                    if (gmailData != null)
                    {
                        List<RootIndMessage> indMessages = await _gmailService.GetIndividualMessages(leadHistoryData.EmailAddress, tokens.AccessToken, gmailData.History);
                        if (indMessages != null && indMessages.Any())
                        {
                            var integrationAccountInfo = (await _integrationRepo.ListAsync(new IntegrationAccByGmailIdOrIdSpec(storedGmaildata.Id)))?.FirstOrDefault() ?? throw new Exception("No integration account found.");
                            List<LeadData> leadDatas = _gmailService.GetLeadWithSource(indMessages);
                            List<IntegrationLeadInfo> leadInfos = leadDatas.Adapt<List<IntegrationLeadInfo>>();
                            leadInfos = leadInfos.DistinctBy(i => i.Mobile).ToList();
                            List<string> newContacts = leadInfos.Select(i => i.Mobile).ToList();
                            //
                            //List<string> storedContactNos = duplicateLeads.Select(i => i.ContactNo[^10..]).ToList();
                            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                            var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);

                            foreach (var leadInfo in leadInfos)
                            {
                                var contactRes = ListingSitesHelper.ConcatenatePhoneNumberV2(countries?.FirstOrDefault()?.DefaultCallingCode, leadInfo.Mobile, globalSettings, null);

                                var lead = new Domain.Entities.Lead()
                                {
                                    Name = leadInfo.Name,
                                    ContactNo = contactRes,
                                    Email = leadInfo.Email,
                                    Notes = leadInfo.Notes,
                                    Enquiries = new List<LeadEnquiry>()
                                    {
                                        new()
                                        {
                                            LeadSource = leadInfo.LeadSource,
                                            SubSource = integrationAccountInfo.AccountName ?? string.Empty,
                                        }
                                    },
                                    ApiKey = integrationAccountInfo.ApiKey,


                                };
                                bool shouldCreateNewLead = true;
                                try
                                {
                                    CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                                    checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                                    checkDuplicateLeadRequest.LeadSource = LeadSource.Gmail;
                                    var result = await _mediator.Send(checkDuplicateLeadRequest);
                                    shouldCreateNewLead = result.Data;
                                }
                                catch(Exception ex) { }
                                
                                if (shouldCreateNewLead)
                                {
                                    if (!string.IsNullOrEmpty(leadInfo.Project))
                                    {
                                        var existingNewProjects = await _projectRepo.ListAsync(new GetAllNewProjectsV2Spec());
                                        List<Lrb.Domain.Entities.Project> projectsList = new();
                                        var newProjects = leadInfo.Project.Split(',');
                                        var existingTempProjectsNames = existingNewProjects.Select(i => i.Name?.ToLower()).ToList();
                                        try
                                        {
                                            if (newProjects != null && newProjects.Length > 0)
                                            {
                                                foreach (var newProject in newProjects.Distinct())
                                                {

                                                    var existingProject = existingNewProjects.FirstOrDefault(i => i.Name?.ToLower() == newProject.ToLower());
                                                    if (existingProject != null)
                                                    {
                                                        if (lead.Projects != null)
                                                        {
                                                            lead.Projects.Add(existingProject);
                                                        }
                                                        else
                                                        {
                                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { existingProject };
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Domain.Entities.Project tempProjects = new() { Name = newProject };
                                                        tempProjects = await _projectRepo.AddAsync(tempProjects, cancellationToken);
                                                        if (lead.Projects != null)
                                                        {
                                                            lead.Projects.Add(tempProjects);
                                                        }
                                                        else
                                                        {
                                                            lead.Projects = new List<Lrb.Domain.Entities.Project>() { tempProjects };
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            var error = new LrbError()
                                            {
                                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                ErrorSource = ex?.Source,
                                                StackTrace = ex?.StackTrace,
                                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                                ErrorModule = "CreateLeadFromPubSubMessageRequest ->Handle()"
                                            };
                                            await _leadRepositoryAsync.AddErrorAsync(error);
                                        }
                                    }
                                    List<Domain.Entities.Lead>? duplicateLeads = null;
                                    var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                                    if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                                    {
                                        if (!duplicateFeatureInfo.AllowAllDuplicates)
                                        {
                                            var duplicateLeadSpecDto = lead.Adapt<DuplicateLeadSpecDto>();
                                            duplicateLeadSpecDto.SubSource = integrationAccountInfo.AccountName ?? string.Empty;
                                            duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto), cancellationToken);
                                        }
                                    }
                                    else
                                    {
                                        duplicateLeads ??= new();
                                        var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec((leadInfo.Mobile?.Length >= 1 ? leadInfo.Mobile : "invalid ContactNo") ?? "invalid ContactNo", leadInfo.Mobile ?? "invalid ContactNo"), cancellationToken);
                                        if (duplicateLead != null)
                                        {
                                            duplicateLeads.Add(duplicateLead);
                                        }
                                    }

                                    if (!duplicateLeads?.Any() ?? true)
                                    {
                                        var enquiry = leadInfo.Adapt<LeadEnquiry>();
                                        await _leadInfoRepositoryAsync.AddAsync(leadInfo);
                                        var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                                        string name = lead.Name.Trim();
                                        lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                                        lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                                        lead.CreatedBy = integrationAccountInfo.CreatedBy;
                                        lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                                        lead.AccountId = integrationAccountInfo.Id;

                                        IntegrationAssignment? integrationAssignmentDetails = null;
                                        integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, LeadSource.Gmail, _integrationAssignmentRepo, integrationAccRepo: _integrationRepo);
                                        //Add enquired location from assigned locaiton in the integration account.
                                        if (integrationAssignmentDetails?.Location != null)
                                        {
                                            Address? address = null;

                                            var assignedLocation = integrationAssignmentDetails.Location;
                                            var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                                            if (existingAddress != null)
                                            {
                                                address = existingAddress;
                                            }
                                            else
                                            {
                                                address = assignedLocation.MapToAddress();
                                                address.Location = assignedLocation;
                                                await _addressRepo.AddAsync(address);
                                            }
                                            if (address != null)
                                            {
                                                //enquiry.Address = address;
                                                enquiry.Addresses = new List<Address> { address };
                                            }
                                        }

                                        #region Automation
                                        (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                                        globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                                        //var project = await _tempProjectsRepo.FirstOrDefaultAsync(new TempProjectByIdSpec(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);
                                        var project = await _projectRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);
                                        if (integrationAccountInfo != null)
                                        {
                                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Gmail, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project);
                                        }
                                        var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                                        UserDetailsDto? assignedUser = null;
                                        if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                                        {
                                            try
                                            {
                                                assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                                            }
                                            catch (Exception ex)
                                            {
                                            }
                                        }

                                        if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                                        {
                                            lead.AssignTo = existingLead.AssignTo;
                                        }
                                        else
                                        {
                                            List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                                            (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                                            if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased && integrationAccountInfo?.UserAssignment != null)
                                            {
                                                try
                                                {
                                                    integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                                    var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                                    lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                                                }
                                                catch (Exception ex) { }
                                            }
                                            else
                                            {
                                                var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                                                if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                                                {
                                                    bool isAssigned = true;
                                                    while (isAssigned)
                                                    {
                                                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.GoogleAds, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, priority: userAssignmentAndProject.Priority);
                                                        assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                                        if (assignToRes.AssignTo != Guid.Empty)
                                                        {
                                                            isAssigned = false;
                                                        }
                                                        else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                                        {
                                                            userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                                        }
                                                        else
                                                        {
                                                            isAssigned = false;
                                                        }
                                                    }
                                                }
                                                lead.AssignTo = assignToRes.AssignTo;
                                            }
                                            if ((globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                                            {
                                                (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, lead.ContactNo) : (Guid.Empty, false);
                                                lead.SecondaryUserId = secondaryAssignTo.AssignTo;
                                            }

                                            // Set OriginalOwner to the assigned user when first assigned
                                            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                                            {
                                                lead.OriginalOwner = lead.AssignTo;
                                            }

                                            _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                            _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                                        }
                                        try
                                        {
                                            IntegrationAssignment integrationAssignment = await IntegrationAssignmentHelper.GetAssignedProjLocAsyncV1(source: LeadSource.Gmail, intgrAccId: integrationAccountInfo?.Id, integrationAccRepo: _integrationAccRepo);

                                            //(Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(source: LeadSource.Gmail, intgrAccId: integrationAccountInfo?.Id, integrationAccRepo: _integrationAccRepo);
                                            var projectToAssign = userAssignmentAndProject.Project ?? integrationAssignment?.Project;
                                            if (lead.Projects != null && projectToAssign != null)
                                            {
                                                lead.Projects.Add(projectToAssign);
                                            }
                                            else if (projectToAssign != null)
                                            {
                                                lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAssign };
                                            }
                                            if (lead.Agencies != null && integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                                            {
                                                lead.Agencies.Add(integrationAssignment?.Agency);
                                            }
                                            else if (integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                                            {
                                                lead.Agencies ??= new List<Lrb.Domain.Entities.Agency>() { integrationAssignment?.Agency };
                                            }

                                            if (lead.Campaigns != null && integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                                            {
                                                lead.Campaigns.Add(integrationAssignment?.Campaign);
                                            }
                                            else if (integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                                            {
                                                lead.Campaigns ??= new List<Lrb.Domain.Entities.Campaign>() { integrationAssignment?.Campaign };
                                            }
                                            if (lead.ChannelPartners != null && integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                                            {
                                                lead.ChannelPartners.Add(integrationAssignment?.ChannelPartner);
                                            }
                                            else if (integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                                            {
                                                lead.ChannelPartners ??= new List<Lrb.Domain.Entities.ChannelPartner>() { integrationAssignment?.ChannelPartner };
                                            }

                                            if (lead.Properties != null && integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false)
                                            {
                                                lead.Properties.Add(integrationAssignment?.Property);
                                            }
                                            else if (integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false && integrationAssignment?.Property?.IsArchived == false)
                                            {
                                                lead.Properties ??= new List<Lrb.Domain.Entities.Property>() { integrationAssignment?.Property };
                                            }
                                        }
                                        catch
                                        {

                                        }
                                        #endregion
                                        lead.TagInfo = new();
                                        #region DuplicateDetails
                                        var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                                        if (parentLead != null)
                                        {
                                            lead = lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                                            parentLead.ChildLeadsCount += 1;
                                            try
                                            {
                                                await _leadRepo.UpdateAsync(parentLead);
                                            }
                                            catch (Exception ex) { }

                                        }
                                        #endregion
                                        if (lead.AssignTo != Guid.Empty)
                                        {
                                            lead.AssignDate = DateTime.UtcNow;
                                        }
                                        await _leadRepo.AddAsync(lead);
                                        enquiry.LeadId = lead.Id;
                                        enquiry.SubSource = integrationAccountInfo?.AccountName;
                                        enquiry.IsPrimary = true;
                                        enquiry.Currency = leadInfo?.Currency ?? countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                                        await _leadEnquiryRepo.AddAsync(enquiry);
                                        #region createDuplicateLead
                                        var totalLeadsCount = 0;
                                        try
                                        {
                                            if (integrationAccountInfo != null)
                                            {
                                                userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.Gmail, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project);
                                            }
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                                            {
                                                var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                                                if (duplicateLeadAssignmentsIds?.Any() ?? false)
                                                {
                                                    if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                                                    {
                                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, lead.ContactNo);
                                                    }
                                                    else
                                                    {
                                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, lead.ContactNo);
                                                        await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);
                                                    }
                                                }

                                            }
                                        }
                                        catch (Exception ex) { }
                                        try
                                        {
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                                            {
                                                var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                                try
                                                {
                                                    if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                                                    {
                                                        await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, lead.ContactNo);
                                                        await _leadRepo.UpdateRangeAsync(replicatedLeads);
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                }
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion
                                        var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?[0];
                                        var leadDto = fullLead?.Adapt<ViewLeadDto>();
                                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                                        if (integrationAccountInfo != null)
                                        {
                                            integrationAccountInfo.LeadCount++;
                                            integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                                        }
                                        var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                                        try
                                        {
                                            await _leadHistoryRepo.AddAsync(leadHsitory);
                                        }
                                        catch { }
                                        #region DuplicateLead History
                                        try
                                        {
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                                            {
                                                var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                                if (totalDuplicateLeads?.Any() ?? false)
                                                {
                                                    await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                                                }
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion

                                        #region Assignment History
                                        try
                                        {
                                            if (lead.AssignTo != Guid.Empty)
                                            {
                                                await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion

                                        #region Push Notification
                                        try
                                        {
                                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                                            List<string> notificationResponses = new();
                                            string? tenantId = await _npgsqlRepo.GetTenantId(integrationAccountInfo?.Id ?? default);
                                            List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                                            if (fullLead != null && (fullLead.AssignTo == default || fullLead.AssignTo == Guid.Empty))
                                            {
                                                _logger.Information($"CreateLeadFromPubSubMessageRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                                if (adminIds.Any())
                                                {
                                                    List<string> notificationSchduleResponse = new();
                                                    if (_isDupicateUnassigned)
                                                    {
                                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.DuplicateUnAssigment, lead, null, null, null, null, null, adminIds);
                                                    }
                                                    else
                                                    {
                                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, null, null, null, null, null, adminIds);
                                                    }
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            else if (fullLead.AssignTo != Guid.Empty)
                                            {
                                                var user = await _userService.GetAsync(fullLead.AssignTo.ToString(), cancellationToken);
                                                if (user != null)
                                                {
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                                List<Guid> userWithManagerIds = new();
                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                {
                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                                    userWithManagerIds.AddRange(managerIds);
                                                }
                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                {
                                                    userWithManagerIds.AddRange(adminIds);
                                                }
                                                if (user != null && userWithManagerIds.Any())
                                                {
                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                    userWithManagerIds.Remove(lead.AssignTo);
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            _logger.Information($"CreateLeadFromPubSubMessageRequest  -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                                            {
                                                var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                                if (allduplicateLeads?.Any() ?? false)
                                                {
                                                    foreach (var duplicatelead in allduplicateLeads)
                                                    {
                                                        try
                                                        {
                                                            if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                                            {
                                                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                                                if (user != null)
                                                                {
                                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                                }
                                                                List<Guid> userWithManagerIds = new();
                                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                                {
                                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                                                    userWithManagerIds.AddRange(managerIds);
                                                                }
                                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                                {
                                                                    userWithManagerIds.AddRange(adminIds);
                                                                }
                                                                if (user != null && userWithManagerIds.Any())
                                                                {
                                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                                    userWithManagerIds.Remove(duplicatelead.AssignTo);
                                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                                }
                                                            }
                                                            _logger.Information($"CreateLeadFromPubSubMessageRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                                        }
                                                        catch (Exception ex)
                                                        {
                                                            _logger.Information($"CreateLeadFromPubSubMessageRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                                            var error = new LrbError()
                                                            {
                                                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                                ErrorSource = ex?.Source,
                                                                StackTrace = ex?.StackTrace,
                                                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                                            };
                                                            await _leadRepositoryAsync.AddErrorAsync(error);

                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.Information($"CreateLeadFromPubSubMessageRequest  -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                        }
                                        #endregion

                                        #region Lead Rotation
                                        try
                                        {
                                            if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                                            {
                                                if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                                {
                                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                                }
                                            }
                                            else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                            {
                                                if (lead.AssignTo != Guid.Empty)
                                                {
                                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);

                                                }
                                            }
                                        }
                                        catch (Exception ex) { }
                                        #endregion
                                    }
                                }
                            }
                            if (integrationAccountInfo != null)
                            {
                                await _integrationAccRepo.UpdateAsync(integrationAccountInfo);
                            }
                        }
                    }
                }
                return new(returnValue);
            }
            catch (Exception e)
            {
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CreateLeadFromPubSubMessageRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return new(true);
            }
        }
        protected async Task<(string ContactNo, string? AltContactNo)> ValidateContactNoAsync(string contactNo, string? alternateContactNo, CancellationToken cancellationToken = default)
        {
            try
            {
                var globalSettingInfo = await _globalSettingsRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var hasInternationalSupportEnabled = globalSettingInfo?.FirstOrDefault()?.HasInternationalSupport ?? false;
                if (contactNo.Length == 10 && !hasInternationalSupportEnabled)
                {
                    contactNo = $"+91{contactNo.Trim()}";
                }
                bool IsValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(contactNo, hasInternationalSupportEnabled);
                if (!IsValidContactNo) { throw new Exception("Invalid ContactNo"); }
                if (alternateContactNo != null)
                {
                    if (alternateContactNo.Length == 10 && !hasInternationalSupportEnabled)
                    {
                        alternateContactNo = $"+91{alternateContactNo}";
                    }
                    IsValidContactNo = PhoneNumberValidationHelper.ValidateContactNoUsingRegex(alternateContactNo, hasInternationalSupportEnabled);
                    if (!IsValidContactNo) { throw new Exception("Invalid AltNumber"); }
                }
                return (contactNo, alternateContactNo);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Serialize());
                throw;
            }
        }
    }
}
