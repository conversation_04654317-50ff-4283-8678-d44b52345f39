﻿using Amazon.CognitoIdentityProvider.Model.Internal.MarshallTransformations;
using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Utils;
using Mapster;
using Microsoft.Graph;
using Newtonsoft.Json;
using Serilog;
using System.ComponentModel.DataAnnotations;


namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class ExtractFacebookDataRequest : IRequest<Response<Lrb.Application.Integration.Web.Requests.Facebook.LeadInfoDto>>
    {
        public FacebookPageWebhookDto Dto { get; set; } = default!;
        public string TenantId { get; set; } = default!;
        public int RotationCounter { get; set; } = 0;
    }
    public class ExtractFacebookDataRequestHandler : FBCommonHandler, IRequestHandler<ExtractFacebookDataRequest, Response<LeadInfoDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInfoRepo;
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsInfoRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<FacebookLeadInfo> _fbLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IMediator _mediator;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadEnquiry> _leadEnquiryRepo;
        public ExtractFacebookDataRequestHandler(
            IFacebookService facebookService,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
            IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            ILogger logger,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInfoRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsInfoRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            ICurrentUser currentUser,
            INotificationSenderService notificationSenderServiceRepo,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            IJobService hangfireService,
            ITenantIndependentRepository repository,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,

            IRepositoryWithEvents<FacebookLeadInfo> fbLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<Address> addressRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IMediator mediator,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IGoogleAdsService googleAdsService,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
            : base(facebookAuthResponseRepo,
                facebookConnectedPageAccountRepo,
                facebookLeadGenFormRepo,
                facebookService,
                hangfireService,
                repository,
                currentUser,
                integrationAccInfoRepo,
                fbAdsInfoRepo,
                leadRepositoryAsync,
                googleAdsService,
                googleAdsAuthResponseRepo,
                googleAdsRepo,
                googleCampaignsRepo)
        {
            _leadRepo = leadRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _logger = logger;
            _integrationAssignmentInfoRepo = integrationAssignmentInfoRepo;
            _fbAdsInfoRepo = fbAdsInfoRepo;
            _projectRepo = projectRepo;
            _notificationSenderService = notificationSenderServiceRepo;
            _npgsqlRepo = npgsqlRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _fbLeadInfoRepo = fbLeadInfoRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _addressRepo = addressRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _mediator = mediator;
            _leadRotationService = leadRotationService;
            _leadEnquiryRepo = leadEnquiryRepo;
        }

        public async Task<Response<Lrb.Application.Integration.Web.Requests.Facebook.LeadInfoDto>> Handle(ExtractFacebookDataRequest request, CancellationToken cancellationToken)
        {
            if (request.RotationCounter < 2)
            {
                try
                {
                    Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                    var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);
                    var leadgenInfo = request.Dto?.entry?.FirstOrDefault()?.changes?.FirstOrDefault();
                    var formId = leadgenInfo?.value?.form_id ?? string.Empty;
                    var leadgenId = leadgenInfo?.value?.leadgen_id ?? string.Empty;
                    var pageId = leadgenInfo?.value?.page_id ?? string.Empty;
                    var adId = leadgenInfo?.value?.ad_id ?? string.Empty;
                    var createdTime = leadgenInfo?.value?.created_time ?? 0;

                    var ad = await _fbAdsInfoRepo.FirstOrDefaultAsync(new FacebookAdsByAdIdSpec(adId), cancellationToken);
                    var formData = await _facebookLeadGenFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByFormIdSpec(formId), cancellationToken);

                    await _npgsqlRepo.RestoreFBPageAsync(pageId, request.TenantId);
                    var fbPageAccount = await _facebookConnectedPageAccountRepo.FirstOrDefaultAsync(new GetFacebookConnectedPageAccountByFBIdSpec(pageId)) ?? throw new Exception("No Facebook account found by the page id.");
                    var fbAuthResponse = fbPageAccount.FacebookAuthResponse;
                    var integrationAccountInfo = await _integrationAccInfoRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(fbPageAccount.FacebookAuthResponseId), cancellationToken);
                    if ((ad != null && ad.IsSubscribed) || (formData != null && formData.IsSubscribed))
                    {
                        try
                        {
                            if (integrationAccountInfo == null)
                            {
                                integrationAccountInfo = new IntegrationAccountInfo()
                                {
                                    Id = Guid.NewGuid(),
                                    AccountName = fbPageAccount.FacebookAuthResponse?.FacebookAccountName,
                                    LeadSource = LeadSource.Facebook,
                                    LicenseId = Guid.NewGuid(),
                                    JsonTemplate = "",
                                    FacebookAccountId = fbPageAccount.FacebookAuthResponse?.Id ?? Guid.Empty,
                                };
                                await _integrationAccInfoRepo.AddAsync(integrationAccountInfo);
                            }
                            var fbLeadData = await _facebookService.GetLeadInfoAsync(leadgenId, fbPageAccount.LongLivedPageAccessToken);
                            var fbLead = fbLeadData.FBLead;
                            if (fbLeadData.IsActive != fbAuthResponse.IsActive)
                            {
                                fbAuthResponse.IsActive = fbLeadData.IsActive;
                                await _facebookAuthResponseRepo.UpdateAsync(fbAuthResponse, cancellationToken);
                            }
                            if (fbLead == null)
                            {
                                //return new(false, "Something went wrong in Your Permissions and Your Facebook Account please Check it and Try again.");
                                return new("There is a permissions issue or a problem with the Facebook account. Please check and try again.");
                            }
                            fbLead.CountryCode = ad?.CountryCode ?? formData?.CountryCode ?? countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";

                            try
                            {
                                if ((await _fbLeadInfoRepo.GetByIdAsync(fbLead.Id, cancellationToken)) == null)
                                {
                                    await _fbLeadInfoRepo.AddAsync(fbLead.Adapt<FacebookLeadInfo>(), cancellationToken);
                                }
                            }
                            catch (Exception e)
                            {
                                var error = new LrbError()
                                {
                                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                    ErrorSource = e?.Source,
                                    StackTrace = e?.StackTrace,
                                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    ErrorModule = "ProcessFacebookWebhookRequestHandler -> Handle()"
                                };
                                await _leadRepositoryAsync.AddErrorAsync(error);
                            }
                           
                            var lead = fbLead.MapToGenericLead(ad, formData, globalSettings?.IsInstagramSourceEnabled ?? false, globalSettings, leadgenId, createdTime);
                            var leadinfo = lead?.Adapt<Lrb.Application.Integration.Web.Requests.Facebook.LeadInfoDto>();
                            return new Response<LeadInfoDto>(leadinfo);
                        }
                        catch {
                            return null;
                        }
                    }
                }
                catch
                {
                    return null;
                }
            }
            return null;
        }
    }

    public class LeadInfoDto
    {
        public string? Name { get; set;}
        public string? ContactNo { get; set; } 
        public string? AlternateContactNo { get; set; }
        public string? LandLine { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? BookedUnderName { get; set; }
        public string? LeadNumber { get; set; }
        public Guid AssignTo { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
    }
}
