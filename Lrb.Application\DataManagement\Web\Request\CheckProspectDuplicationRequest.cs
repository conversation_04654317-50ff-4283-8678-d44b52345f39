﻿using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web;
using Lrb.Domain.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Lrb.Application.DataManagement.Web.Specs.GetProspectByContactNoSpecs;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class CheckProspectDuplicationRequest : IRequest<Response<ProspectConvertCheckDto>>
    {
        public string? ContactNo { get; set; }
        public CheckProspectDuplicationRequest(string? contactNo)
        {
            ContactNo = contactNo;
        }

        public class CheckLeadDuplicationRequestHandler : IRequestHandler<CheckProspectDuplicationRequest, Response<ProspectConvertCheckDto>>
        {
            private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
            private readonly ICurrentUser _currentUser;
            protected readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
            private readonly IReadRepository<Lrb.Domain.Entities.GlobalSettings> _globalSettingsRepo;
            private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;

            public CheckLeadDuplicationRequestHandler(IRepositoryWithEvents<Prospect> prospectRepo, ICurrentUser currentUser, IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo, IReadRepository<Lrb.Domain.Entities.GlobalSettings> globalSettingsRepository, IRepositoryWithEvents<Domain.Entities.Lead> leadRepo)

            {
                _prospectRepo = prospectRepo;
                _currentUser = currentUser;
                _duplicateInfoRepo = duplicateInfoRepo;
                _globalSettingsRepo = globalSettingsRepository;
                _leadRepo = leadRepo;
            }

            public async Task<Response<ProspectConvertCheckDto>> Handle(CheckProspectDuplicationRequest request, CancellationToken cancellationToken)
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var countries = JsonConvert.DeserializeObject<List<CountryInfo>>(globalSettings?.CountriesInfo);
                var countryCode = countries.FirstOrDefault().DefaultCallingCode;
                if (string.IsNullOrEmpty(countryCode)) { throw new Exception("Country Code field can not be empty."); }
                var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                
                var userId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var prospect = await _leadRepo.FirstOrDefaultAsync(new CheckLeadByContactNoSpec(request.ContactNo, countryCode), cancellationToken);
                
                if (prospect != null)
                {
                    if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded && duplicateFeatureInfo.AllowAllDuplicates)
                    {
                        return new(new ProspectConvertCheckDto() { Id = prospect.Id, CanNavigate = true, Duplicate = true });
                    }
                    return new(new ProspectConvertCheckDto() { Id = prospect.Id, CanNavigate = true,Duplicate = false });
                }
                return new(null);
            }
        }
    }
}
